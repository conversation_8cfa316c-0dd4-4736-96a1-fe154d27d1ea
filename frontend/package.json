{"name": "chatbot-straico-frontend", "version": "1.0.0", "description": "Frontend React application for RAG-based chatbot using Straico", "private": true, "dependencies": {"@types/node": "^16.18.68", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "date-fns": "^3.0.6", "framer-motion": "^10.16.16", "react-dropzone": "^14.2.3", "react-intersection-observer": "^9.5.3"}, "devDependencies": {"@types/react-syntax-highlighter": "^15.5.11", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}