@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
  }
}

@layer components {
  /* Chat message styles */
  .message-user {
    @apply bg-primary-600 text-white rounded-lg rounded-br-sm px-4 py-2 max-w-xs md:max-w-md lg:max-w-lg ml-auto;
  }
  
  .message-assistant {
    @apply bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg rounded-bl-sm px-4 py-2 max-w-xs md:max-w-md lg:max-w-lg mr-auto border border-gray-200 dark:border-gray-700;
  }
  
  /* Typing indicator */
  .typing-indicator {
    @apply flex space-x-1;
  }

  .typing-dot {
    @apply w-2 h-2 bg-gray-400 rounded-full animate-pulse;
    animation-delay: 0ms;
  }

  .typing-dot:nth-child(2) {
    animation-delay: 150ms;
  }

  .typing-dot:nth-child(3) {
    animation-delay: 300ms;
  }

  /* Thinking bubble */
  .thinking-bubble {
    @apply relative bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl px-4 py-3 max-w-xs border border-blue-200 dark:border-blue-700/50;
    animation: float 3s ease-in-out infinite;
  }

  .thinking-bubble-content {
    @apply flex flex-col space-y-2;
  }

  .thinking-text {
    @apply text-sm text-blue-700 dark:text-blue-300 font-medium;
    animation: fadeInOut 2s ease-in-out infinite;
  }

  .thinking-dots {
    @apply flex space-x-1 justify-center;
  }

  .thinking-dot {
    @apply w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full;
    animation: bounce 1.4s ease-in-out infinite both;
  }

  .thinking-dot:nth-child(1) {
    animation-delay: -0.32s;
  }

  .thinking-dot:nth-child(2) {
    animation-delay: -0.16s;
  }

  .thinking-bubble-tail {
    @apply absolute w-3 h-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-l border-b border-blue-200 dark:border-blue-700/50 transform rotate-45;
    left: -6px;
    top: 50%;
    margin-top: -6px;
  }

  /* Animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  @keyframes fadeInOut {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  @keyframes bounce {
    0%, 80%, 100% {
      transform: scale(0);
    }
    40% {
      transform: scale(1);
    }
  }
  
  /* Custom scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
  
  /* File upload area */
  .file-upload-area {
    @apply border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-primary-400 dark:hover:border-primary-500 transition-colors duration-200;
  }
  
  .file-upload-area.dragover {
    @apply border-primary-500 bg-primary-50 dark:bg-primary-900/20;
  }
  
  /* Button variants */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  /* Input styles */
  .input-primary {
    @apply block w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }
  
  /* Card styles */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* Fade in animation */
  .fade-in {
    @apply animate-fade-in;
  }
  
  /* Slide up animation */
  .slide-up {
    @apply animate-slide-up;
  }

  /* Message highlight animation */
  .highlight-message {
    animation: highlightPulse 2s ease-in-out;
  }

  @keyframes highlightPulse {
    0% { background-color: transparent; }
    50% { background-color: rgba(59, 130, 246, 0.1); }
    100% { background-color: transparent; }
  }
}

@layer utilities {
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Text gradient */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
  }
  
  /* Glass effect */
  .glass {
    @apply bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm;
  }
  
  /* Truncate text with ellipsis */
  .truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Dark mode specific styles */
.dark {
  color-scheme: dark;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .message-user {
    @apply border-2 border-white;
  }
  
  .message-assistant {
    @apply border-2 border-gray-900 dark:border-white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-pulse,
  .animate-spin,
  .animate-fade-in,
  .animate-slide-up {
    animation: none;
  }
}