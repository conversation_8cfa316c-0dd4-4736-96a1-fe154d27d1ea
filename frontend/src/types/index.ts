// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  details?: any;
}

// Chat types
export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}



export interface Conversation {
  id: string;
  userId: string;
  messages: Message[];
  created: string;
  lastActivity: string;
  messageCount: number;
  metadata?: Record<string, any>;
}

export interface ConversationSummary {
  id: string;
  created: string;
  lastActivity: string;
  messageCount: number;
  lastMessage: string;
}

// Chat service types
export interface SendMessageRequest {
  message: string;
  conversationId?: string;
  userId?: string;
  ragId?: string;
  systemPrompt?: string;
}

export interface SendMessageResponse {
  messageId: string;
  response: string;
  conversationId: string;
  timestamp: string;
}

// File types
export interface FileInfo {
  id: string;
  name: string;
  path: string;
  relativePath: string;
  size: number;
  type: string;
  lastModified: string;
  created: string;
}

export interface UploadedFile {
  id: string;
  name: string;
  fileName: string;
  path: string;
  size: number;
  type: string;
  uploadDate: string;
  mimetype: string;
}

export interface FileUploadProgress {
  fileId: string;
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
}

// RAG types
export interface RAGInfo {
  ragId: string;
  name: string;
  description: string;
  status: 'creating' | 'active' | 'error' | 'updating';
  documentsCount: number;
  createdAt: string;
  lastUpdated?: string;
}

export interface RAGDocument {
  id: string;
  title: string;
  content: string;
  metadata: {
    sourceFile: string;
    filePath: string;
    fileType: string;
    chunkIndex: number;
    totalChunks: number;
    lastModified: string;
    fileSize: number;
  };
}

export interface RAGPreview {
  preview: Array<{
    id: string;
    title: string;
    contentPreview: string;
    contentLength: number;
    metadata: RAGDocument['metadata'];
  }>;
  totalDocuments: number;
  previewCount: number;
  totalContentLength: number;
}

// Socket.IO event types
export interface SocketEvents {
  // User events
  'user:identify': (userData: { userId?: string; name?: string }) => void;
  'user:identified': (data: { user: User }) => void;

  // Chat events
  'chat:message': (messageData: SendMessageRequest) => void;
  'chat:response': (response: SendMessageResponse) => void;
  'chat:error': (error: { conversationId: string; error: string; details?: string }) => void;
  'chat:typing': (data: { conversationId: string }) => void;
  'chat:stop_typing': (data: { conversationId: string }) => void;
  'chat:bot_typing': (data: { conversationId: string }) => void;
  'chat:bot_stopped_typing': (data: { conversationId: string }) => void;
  'chat:user_typing': (data: { userId: string; userName: string; conversationId: string }) => void;
  'chat:user_stopped_typing': (data: { userId: string; conversationId: string }) => void;
  'chat:new_message': (data: { conversationId: string; message: Message }) => void;

  // Conversation events
  'conversation:join': (conversationId: string) => void;
  'conversation:leave': (conversationId: string) => void;
  'conversation:joined': (data: { conversationId: string }) => void;
  'conversation:left': (data: { conversationId: string }) => void;
  'conversation:history': (history: Conversation) => void;
  'conversation:user_joined': (data: { userId: string; userName: string; conversationId: string }) => void;
  'conversation:user_left': (data: { userId: string; conversationId: string }) => void;

  // File events
  'file:upload_progress': (data: FileUploadProgress) => void;

  // RAG events
  'rag:status': (ragId: string) => void;
  'rag:status_update': (data: RAGInfo) => void;

  // Error events
  'error': (error: { message: string }) => void;
}

// User types
export interface User {
  id: string;
  name: string;
  socketId: string;
  connectedAt: string;
}

// UI State types
export interface ChatState {
  currentConversation: Conversation | null;
  conversations: ConversationSummary[];
  isLoading: boolean;
  isTyping: boolean;
  isBotTyping: boolean;
  error: string | null;
  ragId: string | null;
}

export interface FileState {
  files: FileInfo[];
  uploadedFiles: UploadedFile[];
  isLoading: boolean;
  uploadProgress: Record<string, FileUploadProgress>;
  error: string | null;
}

export interface RAGState {
  currentRAG: RAGInfo | null;
  isCreating: boolean;
  isUpdating: boolean;
  error: string | null;
  preview: RAGPreview | null;
}

export interface AppState {
  user: User | null;
  isConnected: boolean;
  theme: 'light' | 'dark' | 'system';
  sidebarOpen: boolean;
  currentView: 'chat';
}

// Configuration types
export interface AppConfig {
  apiUrl: string;
  socketUrl: string;
  maxFileSize: number;
  allowedFileTypes: string[];
  chatConfig: {
    maxMessageLength: number;
    typingTimeout: number;
    messageRetryAttempts: number;
  };
}

// Statistics types
export interface ChatStatistics {
  totalConversations: number;
  totalMessages: number;
  activeConversations: number;
  averageMessagesPerConversation: number;
}

export interface FileStatistics {
  totalFiles: number;
  totalSize: number;
  fileTypes: Record<string, number>;
  lastModified: string | null;
}

// Search types
export interface SearchResult {
  conversationId: string;
  messageId: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  context: Message[];
}

export interface SearchRequest {
  query: string;
  userId?: string;
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  query: string;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'file';
  required?: boolean;
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
  };
}

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}