import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  Cog6ToothIcon,
  Bars3Icon,
  XMarkIcon,
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
  WifiIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

import { User, Theme, AppState } from '../../types';

interface LayoutProps {
  children: React.ReactNode;
  user: User | null;
  isConnected: boolean;
  theme: Theme;
  sidebarOpen: boolean;
  currentView: AppState['currentView'];
  onToggleTheme: () => void;
  onToggleSidebar: () => void;
  onChangeView: (view: AppState['currentView']) => void;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  user,
  isConnected,
  theme,
  sidebarOpen,
  currentView,
  onToggleTheme,
  onToggleSidebar,
  onChangeView
}) => {
  const location = useLocation();

  const navigation = [
    {
      name: 'Chat',
      href: '/',
      icon: ChatBubbleLeftRightIcon,
      view: 'chat' as const,
      current: location.pathname === '/'
    },
    {
      name: 'File',
      href: '/files',
      icon: DocumentTextIcon,
      view: 'files' as const,
      current: location.pathname === '/files'
    },
    {
      name: 'Amministrazione',
      href: '/admin',
      icon: Cog6ToothIcon,
      view: 'admin' as const,
      current: location.pathname === '/admin'
    }
  ];

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return SunIcon;
      case 'dark':
        return MoonIcon;
      default:
        return ComputerDesktopIcon;
    }
  };

  const ThemeIcon = getThemeIcon();

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <div className={`
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                  <ChatBubbleLeftRightIcon className="w-5 h-5 text-white" />
                </div>
              </div>
              <div className="ml-3">
                <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Softway Chat
                </h1>
              </div>
            </div>
            <button
              onClick={onToggleSidebar}
              className="lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-4 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => onChangeView(item.view)}
                  className={`
                    ${item.current
                      ? 'bg-primary-50 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300 border-primary-500'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 border-transparent'
                    }
                    group flex items-center px-3 py-2 text-sm font-medium rounded-lg border-l-4 transition-colors duration-200
                  `}
                >
                  <Icon className="mr-3 h-5 w-5 flex-shrink-0" />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* Connection Status */}
          <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className={`
                w-2 h-2 rounded-full mr-2
                ${isConnected ? 'bg-green-500' : 'bg-red-500'}
              `} />
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {isConnected ? 'Connesso' : 'Disconnesso'}
              </span>
              {!isConnected && (
                <ExclamationTriangleIcon className="w-4 h-4 ml-1 text-yellow-500" />
              )}
            </div>
          </div>

          {/* User Info */}
          <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {user?.name?.charAt(0).toUpperCase() || 'U'}
                  </span>
                </div>
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {user?.name || 'Utente'}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {user?.id || 'Non identificato'}
                </p>
              </div>
            </div>
          </div>

          {/* Theme Toggle */}
          <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={onToggleTheme}
              className="flex items-center w-full px-3 py-2 text-sm text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
            >
              <ThemeIcon className="w-5 h-5 mr-3" />
              <span className="capitalize">{theme === 'system' ? 'Sistema' : theme === 'light' ? 'Chiaro' : 'Scuro'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={onToggleSidebar}
        />
      )}

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top bar */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between h-16 px-4">
            <div className="flex items-center">
              <button
                onClick={onToggleSidebar}
                className="lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <Bars3Icon className="w-6 h-6" />
              </button>
              <h2 className="ml-2 text-lg font-semibold text-gray-900 dark:text-white lg:ml-0">
                {navigation.find(item => item.current)?.name || 'Chat'}
              </h2>
            </div>

            <div className="flex items-center space-x-4">
              {/* Connection indicator */}
              <div className="flex items-center">
                <WifiIcon className={`
                  w-5 h-5 mr-1
                  ${isConnected ? 'text-green-500' : 'text-red-500'}
                `} />
                <span className="text-sm text-gray-500 dark:text-gray-400 hidden sm:inline">
                  {isConnected ? 'Online' : 'Offline'}
                </span>
              </div>

              {/* User avatar */}
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {user?.name?.charAt(0).toUpperCase() || 'U'}
                  </span>
                </div>
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300 hidden sm:inline">
                  {user?.name || 'Utente'}
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-hidden">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;