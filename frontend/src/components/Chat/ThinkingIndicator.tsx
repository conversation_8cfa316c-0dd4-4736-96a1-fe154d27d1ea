import React from 'react';
import { ComputerDesktopIcon } from '@heroicons/react/24/outline';

interface ThinkingIndicatorProps {
  isVisible: boolean;
}

const ThinkingIndicator: React.FC<ThinkingIndicatorProps> = ({ isVisible }) => {
  if (!isVisible) return null;

  return (
    <div className="flex justify-start mb-4">
      <div className="flex flex-row items-end">
        {/* Avatar */}
        <div className="flex-shrink-0 mr-3">
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <ComputerDesktopIcon className="w-5 h-5 text-white" />
          </div>
        </div>

        {/* Typing indicator bubble */}
        <div className="bg-gray-200 dark:bg-gray-700 rounded-2xl px-4 py-3 max-w-xs">
          <div className="flex items-center space-x-1">
            <span className="text-sm text-gray-600 dark:text-gray-300 mr-2">
              Sta scrivendo
            </span>
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThinkingIndicator;
