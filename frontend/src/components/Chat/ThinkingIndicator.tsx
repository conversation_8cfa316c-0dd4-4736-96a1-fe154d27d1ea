import React, { useState, useEffect } from 'react';
import { ComputerDesktopIcon } from '@heroicons/react/24/outline';

interface ThinkingIndicatorProps {
  isVisible: boolean;
}

const ThinkingIndicator: React.FC<ThinkingIndicatorProps> = ({ isVisible }) => {
  const [currentMessage, setCurrentMessage] = useState(0);
  
  const thinkingMessages = [
    "Sto pensando...",
    "Elaborando la risposta...",
    "Analizzando i documenti...",
    "Quasi pronto...",
    "Preparando la risposta..."
  ];

  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      setCurrentMessage((prev) => (prev + 1) % thinkingMessages.length);
    }, 2000);

    return () => clearInterval(interval);
  }, [isVisible, thinkingMessages.length]);

  if (!isVisible) return null;

  return (
    <div className="flex justify-start mb-4">
      <div className="flex flex-row">
        <div className="flex-shrink-0 mr-3">
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center animate-pulse">
            <ComputerDesktopIcon className="w-5 h-5 text-white" />
          </div>
        </div>
        <div className="relative">
          {/* Fumetto di pensiero */}
          <div className="thinking-bubble">
            <div className="thinking-bubble-content">
              <span className="thinking-text">
                {thinkingMessages[currentMessage]}
              </span>
              <div className="thinking-dots">
                <div className="thinking-dot"></div>
                <div className="thinking-dot"></div>
                <div className="thinking-dot"></div>
              </div>
            </div>
            {/* Coda del fumetto */}
            <div className="thinking-bubble-tail"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThinkingIndicator;
