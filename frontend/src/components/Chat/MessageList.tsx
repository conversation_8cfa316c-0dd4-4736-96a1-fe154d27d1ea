import React, { useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';

// Icons
import {
  UserIcon,
  ComputerDesktopIcon,
  ClipboardDocumentIcon
} from '@heroicons/react/24/outline';
import ThinkingIndicator from './ThinkingIndicator';
import ReasoningDisplay from './ReasoningDisplay';

// Types
import { Message, User } from '../../types';

interface MessageListProps {
  messages: Message[];
  isBotTyping: boolean;
  user: User | null;
}

interface MessageItemProps {
  message: Message;
  user: User | null;
}

const MessageItem: React.FC<MessageItemProps> = ({ message, user }) => {
  const isUser = message.role === 'user';
  const timestamp = new Date(message.timestamp);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`flex max-w-xs md:max-w-md lg:max-w-lg ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* Avatar */}
        <div className={`flex-shrink-0 ${isUser ? 'ml-3' : 'mr-3'}`}>
          <div className={`
            w-8 h-8 rounded-full flex items-center justify-center
            ${isUser 
              ? 'bg-primary-600 text-white' 
              : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
            }
          `}>
            {isUser ? (
              <UserIcon className="w-5 h-5" />
            ) : (
              <ComputerDesktopIcon className="w-5 h-5" />
            )}
          </div>
        </div>

        {/* Message content */}
        <div className={`
          ${isUser ? 'message-user' : 'message-assistant'}
          relative group
        `}>
          {/* Message text */}
          <div className="prose prose-sm max-w-none dark:prose-dark">
            {isUser ? (
              <p className="mb-0 whitespace-pre-wrap">{message.content}</p>
            ) : (
              <ReactMarkdown
                components={{
                  code({ node, className, children, ...props }: any) {
                    const match = /language-(\w+)/.exec(className || '');
                    const inline = !className;
                    return !inline && match ? (
                      <SyntaxHighlighter
                        style={oneDark as any}
                        language={match[1]}
                        PreTag="div"
                        className="rounded-md"
                        {...props}
                      >
                        {String(children).replace(/\n$/, '')}
                      </SyntaxHighlighter>
                    ) : (
                      <code className={className} {...props}>
                        {children}
                      </code>
                    );
                  }
                }}
              >
                {message.content}
              </ReactMarkdown>
            )}
          </div>



          {/* Timestamp and actions */}
          <div className={`
            flex items-center justify-between mt-2 pt-2 border-t border-gray-200 dark:border-gray-600
            ${isUser ? 'border-white/20' : ''}
          `}>
            <span className={`
              text-xs
              ${isUser 
                ? 'text-white/70' 
                : 'text-gray-500 dark:text-gray-400'
              }
            `}>
              {format(timestamp, 'HH:mm', { locale: it })}
            </span>
            
            {!isUser && (
              <button
                onClick={() => copyToClipboard(message.content)}
                className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Copia messaggio"
              >
                <ClipboardDocumentIcon className="w-4 h-4 text-gray-500 dark:text-gray-400" />
              </button>
            )}
          </div>


        </div>
      </div>
    </div>
  );
};



const MessageList: React.FC<MessageListProps> = ({ messages, isBotTyping, user }) => {
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      container.scrollTop = container.scrollHeight;
    }
  }, [messages, isBotTyping]);

  return (
    <div 
      ref={messagesContainerRef}
      className="flex-1 overflow-y-auto p-4 space-y-4 custom-scrollbar"
    >
      {messages.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            <ComputerDesktopIcon className="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Benvenuto nel Chat Assistant
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Inizia una conversazione facendo una domanda sui documenti tecnici di Softway.
            </p>
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                Esempi di domande:
              </h4>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <li>• Come si calibra il corpo farfallato?</li>
                <li>• Quali sono i codici di errore E5?</li>
                <li>• Procedure di manutenzione Symphony ST200</li>
                <li>• Specifiche tecniche Euro 5</li>
              </ul>
            </div>
          </div>
        </div>
      ) : (
        <>
          {messages.map((message) => (
            <MessageItem
              key={message.id}
              message={message}
              user={user}
            />
          ))}
          {isBotTyping && <ThinkingIndicator isVisible={isBotTyping} />}
        </>
      )}
    </div>
  );
};

export default MessageList;