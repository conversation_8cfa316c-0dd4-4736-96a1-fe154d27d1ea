import React, { useEffect, useRef, useState } from 'react';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import {
  UserIcon,
  ComputerDesktopIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';

// Types
import { Message, User } from '../../types';

interface ConversationScrollNavProps {
  messages: Message[];
  user: User | null;
  onScrollToMessage: (messageId: string) => void;
}

const ConversationScrollNav: React.FC<ConversationScrollNavProps> = ({
  messages,
  user,
  onScrollToMessage
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    }
  }, [messages]);

  if (messages.length === 0) {
    return null;
  }

  const getMessagePreview = (content: string): string => {
    return content.length > 50 ? content.substring(0, 50) + '...' : content;
  };



  return (
    <div className="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            🧭 Navigazione ({messages.length})
          </h3>
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-1 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            {isCollapsed ? (
              <ChevronDownIcon className="w-4 h-4" />
            ) : (
              <ChevronUpIcon className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      {/* Messages list */}
      {!isCollapsed && (
        <div
          ref={scrollContainerRef}
          className="flex-1 overflow-y-auto p-4 space-y-2 custom-scrollbar"
        >
          {messages.map((message, index) => {
            const isUser = message.role === 'user';
            const timestamp = new Date(message.timestamp);

            return (
              <div
                key={message.id}
                onClick={() => onScrollToMessage(message.id)}
                className="flex items-start p-2 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                {/* Message number */}
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center mr-3">
                  <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
                    {index + 1}
                  </span>
                </div>

                {/* Message icon */}
                <div className="flex-shrink-0 mr-3">
                  {isUser ? (
                    <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                      <UserIcon className="w-3 h-3 text-white" />
                    </div>
                  ) : (
                    <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center">
                      <ComputerDesktopIcon className="w-3 h-3 text-white" />
                    </div>
                  )}
                </div>

                {/* Message content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium text-gray-900 dark:text-white">
                      {isUser ? 'Utente Anonimo' : 'Assistente'}
                    </span>
                    <span className="text-xs text-gray-400 dark:text-gray-500">
                      {format(timestamp, 'HH:mm', { locale: it })}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-300 line-clamp-2">
                    {getMessagePreview(message.content)}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Collapsed state */}
      {isCollapsed && (
        <div className="p-3 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {messages.length} messaggi
          </p>
        </div>
      )}
    </div>
  );
};

export default ConversationScrollNav;
