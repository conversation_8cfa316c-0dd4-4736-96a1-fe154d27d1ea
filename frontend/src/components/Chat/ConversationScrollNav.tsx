import React, { useEffect, useRef, useState } from 'react';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import {
  UserIcon,
  ComputerDesktopIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';

// Types
import { Message, User } from '../../types';

interface ConversationScrollNavProps {
  messages: Message[];
  user: User | null;
  onScrollToMessage: (messageId: string) => void;
}

const ConversationScrollNav: React.FC<ConversationScrollNavProps> = ({
  messages,
  user,
  onScrollToMessage
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    }
  }, [messages]);

  if (messages.length === 0) {
    return null;
  }

  const getMessagePreview = (content: string): string => {
    return content.length > 50 ? content.substring(0, 50) + '...' : content;
  };

  const getMessageIcon = (role: string) => {
    return role === 'user' ? UserIcon : ComputerDesktopIcon;
  };

  return (
    <div className="w-64 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
          Conversazione ({messages.length})
        </h3>
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="p-1 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          {isCollapsed ? (
            <ChevronDownIcon className="w-4 h-4" />
          ) : (
            <ChevronUpIcon className="w-4 h-4" />
          )}
        </button>
      </div>

      {/* Messages list */}
      {!isCollapsed && (
        <div 
          ref={scrollContainerRef}
          className="flex-1 overflow-y-auto p-2 space-y-2 custom-scrollbar"
        >
          {messages.map((message, index) => {
            const Icon = getMessageIcon(message.role);
            const isUser = message.role === 'user';
            const timestamp = new Date(message.timestamp);

            return (
              <div
                key={message.id}
                onClick={() => onScrollToMessage(message.id)}
                className="flex items-start p-2 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                {/* Message number */}
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center mr-2">
                  <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
                    {index + 1}
                  </span>
                </div>

                {/* Message icon */}
                <div className="flex-shrink-0 mr-2">
                  <div className={`
                    w-6 h-6 rounded-full flex items-center justify-center
                    ${isUser 
                      ? 'bg-primary-100 dark:bg-primary-900' 
                      : 'bg-gray-100 dark:bg-gray-700'
                    }
                  `}>
                    <Icon className={`
                      w-3 h-3
                      ${isUser 
                        ? 'text-primary-600 dark:text-primary-400' 
                        : 'text-gray-600 dark:text-gray-400'
                      }
                    `} />
                  </div>
                </div>

                {/* Message content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <span className={`
                      text-xs font-medium
                      ${isUser 
                        ? 'text-primary-600 dark:text-primary-400' 
                        : 'text-gray-600 dark:text-gray-400'
                      }
                    `}>
                      {isUser ? (user?.name || 'Tu') : 'Assistente'}
                    </span>
                    <span className="text-xs text-gray-400 dark:text-gray-500">
                      {format(timestamp, 'HH:mm', { locale: it })}
                    </span>
                  </div>
                  <p className="text-xs text-gray-700 dark:text-gray-300 mt-1 line-clamp-2">
                    {getMessagePreview(message.content)}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Collapsed state */}
      {isCollapsed && (
        <div className="p-3 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {messages.length} messaggi
          </p>
        </div>
      )}
    </div>
  );
};

export default ConversationScrollNav;
