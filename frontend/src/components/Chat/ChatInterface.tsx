import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'react-hot-toast';

// Components
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import LoadingSpinner from '../Common/LoadingSpinner';

// Services
import socketService from '../../services/socketService';
import apiService from '../../services/apiService';

// Types
import { User, Message, Conversation, SendMessageRequest } from '../../types';

interface ChatInterfaceProps {
  user: User | null;
  isConnected: boolean;
  // Conversation management
  currentConversationId?: string | null;
  onConversationChange?: (conversationId: string | null) => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  user,
  isConnected,
  currentConversationId,
  onConversationChange
}) => {
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [ragId, setRagId] = useState<string | null>(null);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize chat
  useEffect(() => {
    initializeChat();
    setupSocketListeners();

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      // Clean up socket listeners to prevent duplicates
      socketService.removeAllListeners();
    };
  }, [user]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages, isBotTyping]);

  // Handle conversation switching from parent
  useEffect(() => {
    if (currentConversationId && currentConversation?.id !== currentConversationId) {
      loadConversation(currentConversationId);
    }
  }, [currentConversationId]);

  /**
   * Initialize chat interface
   */
  const initializeChat = async () => {
    try {
      setIsLoading(true);

      // Load RAG ID from backend configuration
      try {
        const config = await apiService.getConfig();
        if (config.ragId) {
          setRagId(config.ragId);
          localStorage.setItem('ragId', config.ragId);
        }
      } catch (error) {
        console.error('Failed to load configuration:', error);
        // Fallback to localStorage
        const savedRagId = localStorage.getItem('ragId');
        if (savedRagId) {
          setRagId(savedRagId);
        }
      }

      // Create or load conversation
      const conversationId = localStorage.getItem('currentConversationId');
      if (conversationId && user) {
        try {
          const conversation = await apiService.getConversationHistory(conversationId);
          setCurrentConversation(conversation);
          setMessages(conversation.messages);
          
          // Join conversation via socket
          socketService.joinConversation(conversationId);
        } catch (error) {
          console.error('Failed to load conversation:', error);
          createNewConversation();
        }
      } else {
        createNewConversation();
      }
    } catch (error) {
      console.error('Failed to initialize chat:', error);
      toast.error('Errore durante l\'inizializzazione della chat');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Load a specific conversation
   */
  const loadConversation = async (conversationId: string) => {
    if (!user) return;

    setIsLoading(true);
    try {
      const conversation = await apiService.getConversationHistory(conversationId);
      setCurrentConversation(conversation);
      setMessages(conversation.messages);
      localStorage.setItem('currentConversationId', conversationId);

      // Join conversation via socket
      socketService.joinConversation(conversationId);

      // Notify parent of conversation change
      onConversationChange?.(conversationId);
    } catch (error) {
      console.error('Failed to load conversation:', error);
      toast.error('Errore nel caricamento della conversazione');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Create a new conversation
   */
  const createNewConversation = () => {
    const newConversationId = `conv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const newConversation: Conversation = {
      id: newConversationId,
      userId: user?.id || 'anonymous',
      messages: [],
      created: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      messageCount: 0
    };

    setCurrentConversation(newConversation);
    setMessages([]);
    localStorage.setItem('currentConversationId', newConversationId);

    // Join conversation via socket
    socketService.joinConversation(newConversationId);

    // Notify parent of conversation change
    onConversationChange?.(newConversationId);
  };

  /**
   * Setup Socket.IO event listeners
   */
  const setupSocketListeners = () => {
    // Chat response
    socketService.on('chat:response', (response) => {
      const assistantMessage: Message = {
        id: response.messageId,
        role: 'assistant',
        content: response.response,
        timestamp: response.timestamp,
        reasoning: response.reasoning,
        metadata: response.metadata
      };

      setMessages(prev => [...prev, assistantMessage]);
      setIsBotTyping(false);

      // Update conversation
      if (currentConversation) {
        const updatedConversation = {
          ...currentConversation,
          messages: [...currentConversation.messages, assistantMessage],
          lastActivity: response.timestamp,
          messageCount: currentConversation.messageCount + 1
        };
        setCurrentConversation(updatedConversation);
      }
    });

    // Chat error
    socketService.on('chat:error', (error) => {
      console.error('Chat error:', error);
      toast.error(`Errore: ${error.error}`);
      setIsBotTyping(false);
    });

    // Bot typing indicators
    socketService.on('chat:bot_typing', () => {
      console.log('Bot started typing');
      setIsBotTyping(true);
    });

    socketService.on('chat:bot_stopped_typing', () => {
      console.log('Bot stopped typing');
      setIsBotTyping(false);
    });

    // New message from other users (if multi-user)
    socketService.on('chat:new_message', ({ message }) => {
      setMessages(prev => [...prev, message]);
    });
  };

  /**
   * Send a message
   */
  const sendMessage = async (content: string) => {
    if (!content.trim() || !user || !currentConversation) return;

    const userMessage: Message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      role: 'user',
      content: content.trim(),
      timestamp: new Date().toISOString()
    };

    // Add user message to UI immediately
    setMessages(prev => [...prev, userMessage]);

    // Update conversation
    const updatedConversation = {
      ...currentConversation,
      messages: [...currentConversation.messages, userMessage],
      lastActivity: userMessage.timestamp,
      messageCount: currentConversation.messageCount + 1
    };
    setCurrentConversation(updatedConversation);

    // Prepare message data
    const messageData: SendMessageRequest = {
      message: content.trim(),
      conversationId: currentConversation.id,
      userId: user.id,
      ragId: ragId || undefined
    };

    try {
      // Send via socket for real-time response
      if (isConnected) {
        socketService.sendMessage(messageData);
        setIsBotTyping(true);
      } else {
        // Fallback to API if socket not connected
        const response = await apiService.sendMessage(messageData);

        const assistantMessage: Message = {
          id: response.messageId,
          role: 'assistant',
          content: response.response,
          timestamp: response.timestamp,
          reasoning: response.reasoning,
          metadata: response.metadata
        };

        setMessages(prev => [...prev, assistantMessage]);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Errore durante l\'invio del messaggio');
      
      // Remove user message on error
      setMessages(prev => prev.filter(msg => msg.id !== userMessage.id));
    }
  };

  /**
   * Handle typing indicator
   */
  const handleTyping = () => {
    if (!currentConversation || !isConnected) return;

    socketService.startTyping(currentConversation.id);

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Stop typing after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      socketService.stopTyping(currentConversation.id);
    }, 3000);
  };

  /**
   * Scroll to bottom of messages
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  /**
   * Clear conversation
   */
  const clearConversation = () => {
    if (window.confirm('Sei sicuro di voler cancellare questa conversazione?')) {
      setMessages([]);
      createNewConversation();
      toast.success('Conversazione cancellata');
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            Caricamento chat...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Chat header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Chat Assistente
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {ragId ? 'RAG attivo' : 'Nessuna knowledge base caricata'}
              {!isConnected && ' • Modalità offline'}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={clearConversation}
              className="btn-secondary text-sm"
            >
              Nuova Chat
            </button>
          </div>
        </div>
      </div>

      {/* Messages area */}
      <div className="flex-1 overflow-hidden">
        <MessageList
          messages={messages}
          isBotTyping={isBotTyping}
          user={user}
        />
        <div ref={messagesEndRef} />
      </div>

      {/* Message input */}
      <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <MessageInput
          onSendMessage={sendMessage}
          onTyping={handleTyping}
          disabled={!user || (!isConnected && !ragId)}
          placeholder={
            !user 
              ? 'Utente non identificato...'
              : !isConnected && !ragId
              ? 'Connessione non disponibile...'
              : 'Scrivi un messaggio...'
          }
        />
      </div>
    </div>
  );
};

export default ChatInterface;