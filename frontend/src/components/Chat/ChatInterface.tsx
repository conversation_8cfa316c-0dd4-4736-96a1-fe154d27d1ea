import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'react-hot-toast';

// Components
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import LoadingSpinner from '../Common/LoadingSpinner';
import ConversationScrollNav from './ConversationScrollNav';

// Services
import socketService from '../../services/socketService';
import apiService from '../../services/apiService';

// Types
import { User, Message, Conversation, SendMessageRequest } from '../../types';

interface ChatInterfaceProps {
  user: User | null;
  isConnected: boolean;
  onRegisterClearCallback?: (callback: () => void) => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  user,
  isConnected,
  onRegisterClearCallback
}) => {
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [ragId, setRagId] = useState<string | null>(null);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize chat
  useEffect(() => {
    initializeChat();
    setupSocketListeners();

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      // Clean up socket listeners to prevent duplicates
      socketService.removeAllListeners();
    };
  }, [user]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages, isBotTyping]);

  // Register clear conversation callback
  useEffect(() => {
    if (onRegisterClearCallback) {
      onRegisterClearCallback(clearConversation);
    }
  }, [onRegisterClearCallback]);

  /**
   * Initialize chat interface
   */
  const initializeChat = async () => {
    try {
      setIsLoading(true);

      // Load RAG ID from backend configuration
      try {
        const config = await apiService.getConfig();
        if (config.ragId) {
          setRagId(config.ragId);
          localStorage.setItem('ragId', config.ragId);
        }
      } catch (error) {
        console.error('Failed to load configuration:', error);
        // Fallback to localStorage
        const savedRagId = localStorage.getItem('ragId');
        if (savedRagId) {
          setRagId(savedRagId);
        }
      }

      // Create or load conversation
      const conversationId = localStorage.getItem('currentConversationId');
      if (conversationId && user) {
        try {
          const conversation = await apiService.getConversationHistory(conversationId);
          setCurrentConversation(conversation);
          setMessages(conversation.messages);
          
          // Join conversation via socket
          socketService.joinConversation(conversationId);
        } catch (error) {
          console.error('Failed to load conversation:', error);
          createNewConversation();
        }
      } else {
        createNewConversation();
      }
    } catch (error) {
      console.error('Failed to initialize chat:', error);
      toast.error('Errore durante l\'inizializzazione della chat');
    } finally {
      setIsLoading(false);
    }
  };



  /**
   * Create a new conversation
   */
  const createNewConversation = () => {
    const newConversationId = `conv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const newConversation: Conversation = {
      id: newConversationId,
      userId: user?.id || 'anonymous',
      messages: [],
      created: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      messageCount: 0
    };

    setCurrentConversation(newConversation);
    setMessages([]);
    localStorage.setItem('currentConversationId', newConversationId);

    // Join conversation via socket
    socketService.joinConversation(newConversationId);
  };

  /**
   * Setup Socket.IO event listeners
   */
  const setupSocketListeners = () => {
    // Chat response
    socketService.on('chat:response', (response) => {
      const assistantMessage: Message = {
        id: response.messageId,
        role: 'assistant',
        content: response.response,
        timestamp: response.timestamp
      };

      setMessages(prev => [...prev, assistantMessage]);
      setIsBotTyping(false);

      // Update conversation
      if (currentConversation) {
        const updatedConversation = {
          ...currentConversation,
          messages: [...currentConversation.messages, assistantMessage],
          lastActivity: response.timestamp,
          messageCount: currentConversation.messageCount + 1
        };
        setCurrentConversation(updatedConversation);
      }
    });

    // Chat error
    socketService.on('chat:error', (error) => {
      console.error('Chat error:', error);
      toast.error(`Errore: ${error.error}`);
      setIsBotTyping(false);
    });

    // Bot typing indicators
    socketService.on('chat:bot_typing', () => {
      console.log('Bot started typing');
      setIsBotTyping(true);
    });

    socketService.on('chat:bot_stopped_typing', () => {
      console.log('Bot stopped typing');
      setIsBotTyping(false);
    });

    // New message from other users (if multi-user)
    socketService.on('chat:new_message', ({ message }) => {
      setMessages(prev => [...prev, message]);
    });
  };

  /**
   * Send a message
   */
  const sendMessage = async (content: string) => {
    if (!content.trim() || !user || !currentConversation) return;

    const userMessage: Message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      role: 'user',
      content: content.trim(),
      timestamp: new Date().toISOString()
    };

    // Add user message to UI immediately
    setMessages(prev => [...prev, userMessage]);

    // Update conversation
    const updatedConversation = {
      ...currentConversation,
      messages: [...currentConversation.messages, userMessage],
      lastActivity: userMessage.timestamp,
      messageCount: currentConversation.messageCount + 1
    };
    setCurrentConversation(updatedConversation);

    // Prepare message data
    const messageData: SendMessageRequest = {
      message: content.trim(),
      conversationId: currentConversation.id,
      userId: user.id,
      ragId: ragId || undefined
    };

    try {
      // Send via socket for real-time response
      if (isConnected) {
        socketService.sendMessage(messageData);
        setIsBotTyping(true);
      } else {
        // Fallback to API if socket not connected
        setIsBotTyping(true);

        // Add minimum delay to show typing indicator
        const [response] = await Promise.all([
          apiService.sendMessage(messageData),
          new Promise(resolve => setTimeout(resolve, 1500)) // Minimum 1.5 seconds
        ]);

        const assistantMessage: Message = {
          id: response.messageId,
          role: 'assistant',
          content: response.response,
          timestamp: response.timestamp
        };

        setMessages(prev => [...prev, assistantMessage]);
        setIsBotTyping(false);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Errore durante l\'invio del messaggio');
      setIsBotTyping(false);

      // Remove user message on error
      setMessages(prev => prev.filter(msg => msg.id !== userMessage.id));
    }
  };

  /**
   * Handle typing indicator
   */
  const handleTyping = () => {
    if (!currentConversation || !isConnected) return;

    socketService.startTyping(currentConversation.id);

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Stop typing after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      socketService.stopTyping(currentConversation.id);
    }, 3000);
  };

  /**
   * Scroll to bottom of messages
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  /**
   * Scroll to a specific message
   */
  const scrollToMessage = (messageId: string) => {
    const messageElement = document.getElementById(`message-${messageId}`);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      // Highlight the message briefly
      messageElement.classList.add('highlight-message');
      setTimeout(() => {
        messageElement.classList.remove('highlight-message');
      }, 2000);
    }
  };

  /**
   * Clear conversation
   */
  const clearConversation = () => {
    if (window.confirm('Sei sicuro di voler cancellare questa conversazione?')) {
      setMessages([]);
      createNewConversation();
      toast.success('Conversazione cancellata');
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            Caricamento chat...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex" style={{ height: 'calc(100vh - 64px)' }}>
      {/* Main Chat Area - Full Width */}
      <div className="flex-1 flex flex-col h-full">
        {/* Messages area */}
        <div ref={messagesContainerRef} className="flex-1 overflow-y-auto p-4 pb-0"
             style={{ marginRight: messages.length > 0 ? '320px' : '0' }}>
          <MessageList
            messages={messages}
            isBotTyping={isBotTyping}
            user={user}
          />
          <div ref={messagesEndRef} />
          {/* Add padding at bottom to prevent messages from being hidden behind fixed input */}
          <div className="h-24"></div>
        </div>

        {/* Message input - Fixed at bottom */}
        <div className="fixed bottom-0 left-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4 z-10"
             style={{ right: messages.length > 0 ? '320px' : '0' }}>
          <div className="max-w-none">
            <MessageInput
              onSendMessage={sendMessage}
              onTyping={handleTyping}
              disabled={!user || (!isConnected && !ragId)}
              placeholder="Scrivi un messaggio..."
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              Premi Invio per inviare, SHIFT+Invio per andare a capo
            </p>
          </div>
        </div>
      </div>

      {/* Right Sidebar - Navigation */}
      {messages.length > 0 && (
        <ConversationScrollNav
          messages={messages}
          user={user}
          onScrollToMessage={scrollToMessage}
        />
      )}
    </div>
  );
};

export default ChatInterface;