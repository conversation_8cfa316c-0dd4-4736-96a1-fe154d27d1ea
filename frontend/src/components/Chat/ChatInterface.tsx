import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'react-hot-toast';
import { format } from 'date-fns';

// Components
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import LoadingSpinner from '../Common/LoadingSpinner';
import ConversationScrollNav from './ConversationScrollNav';

// Services
import socketService from '../../services/socketService';
import apiService from '../../services/apiService';

// Types
import { User, Message, Conversation, SendMessageRequest } from '../../types';

interface ChatInterfaceProps {
  user: User | null;
  isConnected: boolean;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  user,
  isConnected
}) => {
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [ragId, setRagId] = useState<string | null>(null);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize chat
  useEffect(() => {
    initializeChat();
    setupSocketListeners();

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      // Clean up socket listeners to prevent duplicates
      socketService.removeAllListeners();
    };
  }, [user]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages, isBotTyping]);



  /**
   * Initialize chat interface
   */
  const initializeChat = async () => {
    try {
      setIsLoading(true);

      // Load RAG ID from backend configuration
      try {
        const config = await apiService.getConfig();
        if (config.ragId) {
          setRagId(config.ragId);
          localStorage.setItem('ragId', config.ragId);
        }
      } catch (error) {
        console.error('Failed to load configuration:', error);
        // Fallback to localStorage
        const savedRagId = localStorage.getItem('ragId');
        if (savedRagId) {
          setRagId(savedRagId);
        }
      }

      // Create or load conversation
      const conversationId = localStorage.getItem('currentConversationId');
      if (conversationId && user) {
        try {
          const conversation = await apiService.getConversationHistory(conversationId);
          setCurrentConversation(conversation);
          setMessages(conversation.messages);
          
          // Join conversation via socket
          socketService.joinConversation(conversationId);
        } catch (error) {
          console.error('Failed to load conversation:', error);
          createNewConversation();
        }
      } else {
        createNewConversation();
      }
    } catch (error) {
      console.error('Failed to initialize chat:', error);
      toast.error('Errore durante l\'inizializzazione della chat');
    } finally {
      setIsLoading(false);
    }
  };



  /**
   * Create a new conversation
   */
  const createNewConversation = () => {
    const newConversationId = `conv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const newConversation: Conversation = {
      id: newConversationId,
      userId: user?.id || 'anonymous',
      messages: [],
      created: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      messageCount: 0
    };

    setCurrentConversation(newConversation);
    setMessages([]);
    localStorage.setItem('currentConversationId', newConversationId);

    // Join conversation via socket
    socketService.joinConversation(newConversationId);
  };

  /**
   * Setup Socket.IO event listeners
   */
  const setupSocketListeners = () => {
    // Chat response
    socketService.on('chat:response', (response) => {
      const assistantMessage: Message = {
        id: response.messageId,
        role: 'assistant',
        content: response.response,
        timestamp: response.timestamp
      };

      setMessages(prev => [...prev, assistantMessage]);
      setIsBotTyping(false);

      // Update conversation
      if (currentConversation) {
        const updatedConversation = {
          ...currentConversation,
          messages: [...currentConversation.messages, assistantMessage],
          lastActivity: response.timestamp,
          messageCount: currentConversation.messageCount + 1
        };
        setCurrentConversation(updatedConversation);
      }
    });

    // Chat error
    socketService.on('chat:error', (error) => {
      console.error('Chat error:', error);
      toast.error(`Errore: ${error.error}`);
      setIsBotTyping(false);
    });

    // Bot typing indicators
    socketService.on('chat:bot_typing', () => {
      console.log('Bot started typing');
      setIsBotTyping(true);
    });

    socketService.on('chat:bot_stopped_typing', () => {
      console.log('Bot stopped typing');
      setIsBotTyping(false);
    });

    // New message from other users (if multi-user)
    socketService.on('chat:new_message', ({ message }) => {
      setMessages(prev => [...prev, message]);
    });
  };

  /**
   * Send a message
   */
  const sendMessage = async (content: string) => {
    if (!content.trim() || !user || !currentConversation) return;

    const userMessage: Message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      role: 'user',
      content: content.trim(),
      timestamp: new Date().toISOString()
    };

    // Add user message to UI immediately
    setMessages(prev => [...prev, userMessage]);

    // Update conversation
    const updatedConversation = {
      ...currentConversation,
      messages: [...currentConversation.messages, userMessage],
      lastActivity: userMessage.timestamp,
      messageCount: currentConversation.messageCount + 1
    };
    setCurrentConversation(updatedConversation);

    // Prepare message data
    const messageData: SendMessageRequest = {
      message: content.trim(),
      conversationId: currentConversation.id,
      userId: user.id,
      ragId: ragId || undefined
    };

    try {
      // Send via socket for real-time response
      if (isConnected) {
        socketService.sendMessage(messageData);
        setIsBotTyping(true);
      } else {
        // Fallback to API if socket not connected
        setIsBotTyping(true);

        // Add minimum delay to show typing indicator
        const [response] = await Promise.all([
          apiService.sendMessage(messageData),
          new Promise(resolve => setTimeout(resolve, 1500)) // Minimum 1.5 seconds
        ]);

        const assistantMessage: Message = {
          id: response.messageId,
          role: 'assistant',
          content: response.response,
          timestamp: response.timestamp
        };

        setMessages(prev => [...prev, assistantMessage]);
        setIsBotTyping(false);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Errore durante l\'invio del messaggio');
      setIsBotTyping(false);

      // Remove user message on error
      setMessages(prev => prev.filter(msg => msg.id !== userMessage.id));
    }
  };

  /**
   * Handle typing indicator
   */
  const handleTyping = () => {
    if (!currentConversation || !isConnected) return;

    socketService.startTyping(currentConversation.id);

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Stop typing after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      socketService.stopTyping(currentConversation.id);
    }, 3000);
  };

  /**
   * Scroll to bottom of messages
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  /**
   * Scroll to a specific message
   */
  const scrollToMessage = (messageId: string) => {
    const messageElement = document.getElementById(`message-${messageId}`);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      // Highlight the message briefly
      messageElement.classList.add('highlight-message');
      setTimeout(() => {
        messageElement.classList.remove('highlight-message');
      }, 2000);
    }
  };

  /**
   * Clear conversation
   */
  const clearConversation = () => {
    if (window.confirm('Sei sicuro di voler cancellare questa conversazione?')) {
      setMessages([]);
      createNewConversation();
      toast.success('Conversazione cancellata');
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            Caricamento chat...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Left Sidebar - Chat List */}
      <div className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">Chat</h2>
          <div className="mb-3">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">Chat Assistente</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {ragId ? 'RAG attivo' : 'RAG non attivo'} • {isConnected ? 'Online' : 'Modalità offline'}
            </p>
          </div>
          <button
            onClick={clearConversation}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors duration-200"
          >
            Nuova Chat
          </button>
        </div>

        {/* Chat Messages Preview */}
        <div className="flex-1 overflow-y-auto p-4">
          {messages.length > 0 ? (
            <div className="space-y-3">
              {messages.slice(-3).map((message) => (
                <div key={message.id} className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <div className="flex-shrink-0">
                      {message.role === 'user' ? (
                        <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                          <span className="text-xs text-white font-medium">U</span>
                        </div>
                      ) : (
                        <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center">
                          <span className="text-xs text-white font-medium">A</span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs text-gray-600 dark:text-gray-300 line-clamp-3">
                        {message.content}
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        {format(new Date(message.timestamp), 'HH:mm')}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-500 dark:text-gray-400 text-sm">
              Nessun messaggio ancora
            </div>
          )}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Messages area */}
        <div ref={messagesContainerRef} className="flex-1 overflow-y-auto p-4">
          <MessageList
            messages={messages}
            isBotTyping={isBotTyping}
            user={user}
          />
          <div ref={messagesEndRef} />
        </div>

        {/* Message input */}
        <div className="border-t border-gray-200 dark:border-gray-700 p-4">
          <MessageInput
            onSendMessage={sendMessage}
            onTyping={handleTyping}
            disabled={!user || (!isConnected && !ragId)}
            placeholder="Scrivi un messaggio..."
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            Premi Invio per inviare, SHIFT+Invio per andare a capo
          </p>
        </div>
      </div>

      {/* Right Sidebar - Navigation */}
      {messages.length > 0 && (
        <ConversationScrollNav
          messages={messages}
          user={user}
          onScrollToMessage={scrollToMessage}
        />
      )}
    </div>
  );
};

export default ChatInterface;