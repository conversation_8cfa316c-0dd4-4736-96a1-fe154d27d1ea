import React, { useState, useRef, useEffect } from 'react';
import { PaperAirplaneIcon, MicrophoneIcon } from '@heroicons/react/24/outline';

interface MessageInputProps {
  onSendMessage: (message: string) => void;
  onTyping?: () => void;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
}

const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  onTyping,
  disabled = false,
  placeholder = 'Scrivi un messaggio...',
  maxLength = 5000
}) => {
  const [message, setMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  }, [message]);

  // Focus textarea on mount
  useEffect(() => {
    if (textareaRef.current && !disabled) {
      textareaRef.current.focus();
    }
  }, [disabled]);

  /**
   * Handle message submission
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim() || disabled) return;
    
    onSendMessage(message.trim());
    setMessage('');
    
    // Clear typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
  };

  /**
   * Handle input change
   */
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    
    if (value.length <= maxLength) {
      setMessage(value);
      
      // Trigger typing indicator
      if (onTyping && value.trim()) {
        onTyping();
        
        // Clear existing timeout
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }
        
        // Set new timeout
        typingTimeoutRef.current = setTimeout(() => {
          // Stop typing indicator after 3 seconds
        }, 3000);
      }
    }
  };

  /**
   * Handle key press
   */
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  /**
   * Handle voice recording (placeholder for future implementation)
   */
  const handleVoiceRecording = () => {
    if (!isRecording) {
      // Start recording
      setIsRecording(true);
      // TODO: Implement voice recording functionality
      console.log('Voice recording started');
    } else {
      // Stop recording
      setIsRecording(false);
      // TODO: Process recorded audio
      console.log('Voice recording stopped');
    }
  };

  const canSend = message.trim().length > 0 && !disabled;
  const characterCount = message.length;
  const isNearLimit = characterCount > maxLength * 0.8;

  return (
    <div className="p-4">
      <form onSubmit={handleSubmit} className="relative">
        <div className="flex items-end space-x-3">
          {/* Text input area */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder={placeholder}
              disabled={disabled}
              rows={1}
              className={`
                w-full resize-none rounded-lg border border-gray-300 dark:border-gray-600 
                bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 
                placeholder-gray-500 dark:placeholder-gray-400
                focus:border-primary-500 focus:ring-primary-500 
                disabled:opacity-50 disabled:cursor-not-allowed
                pr-12 py-3 px-4 text-sm
                transition-colors duration-200
              `}
              style={{ minHeight: '44px', maxHeight: '120px' }}
            />
            
            {/* Character count */}
            {characterCount > 0 && (
              <div className={`
                absolute bottom-1 right-12 text-xs
                ${isNearLimit 
                  ? 'text-red-500 dark:text-red-400' 
                  : 'text-gray-400 dark:text-gray-500'
                }
              `}>
                {characterCount}/{maxLength}
              </div>
            )}
          </div>

          {/* Voice recording button */}
          <button
            type="button"
            onClick={handleVoiceRecording}
            disabled={disabled}
            className={`
              p-3 rounded-lg transition-colors duration-200
              ${isRecording
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300'
              }
              disabled:opacity-50 disabled:cursor-not-allowed
            `}
            title={isRecording ? 'Ferma registrazione' : 'Registra messaggio vocale'}
          >
            <MicrophoneIcon className="w-5 h-5" />
          </button>

          {/* Send button */}
          <button
            type="submit"
            disabled={!canSend}
            className={`
              p-3 rounded-lg transition-colors duration-200
              ${canSend
                ? 'bg-primary-600 hover:bg-primary-700 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
              }
            `}
            title="Invia messaggio"
          >
            <PaperAirplaneIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Help text */}
        <div className="mt-2 flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>
            Premi Invio per inviare, Shift+Invio per andare a capo
          </span>
          {disabled && (
            <span className="text-red-500 dark:text-red-400">
              Chat non disponibile
            </span>
          )}
        </div>
      </form>
    </div>
  );
};

export default MessageInput;