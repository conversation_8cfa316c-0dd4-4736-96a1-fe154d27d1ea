import React, { useState } from 'react';
import {
  ChevronDownIcon,
  ChevronRightIcon,
  LightBulbIcon,
  ClockIcon,
  DocumentTextIcon,
  CpuChipIcon
} from '@heroicons/react/24/outline';

interface ReasoningDisplayProps {
  reasoning?: string;
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
    sources?: string[];
  };
}

const ReasoningDisplay: React.FC<ReasoningDisplayProps> = ({ reasoning, metadata }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!reasoning && !metadata) {
    return null;
  }

  const formatProcessingTime = (ms?: number): string => {
    if (!ms) return 'N/A';
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  return (
    <div className="mt-3 border-t border-gray-200 dark:border-gray-700 pt-3">
      {/* Toggle button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
      >
        {isExpanded ? (
          <ChevronDownIcon className="w-4 h-4 mr-1" />
        ) : (
          <ChevronRightIcon className="w-4 h-4 mr-1" />
        )}
        <LightBulbIcon className="w-4 h-4 mr-1" />
        <span>Processo di ragionamento</span>
        {metadata?.tokens && (
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
            {metadata.tokens} token
          </span>
        )}
      </button>

      {/* Expanded content */}
      {isExpanded && (
        <div className="mt-3 space-y-4">
          {/* Metadata */}
          {metadata && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              {metadata.model && (
                <div className="flex items-center text-sm">
                  <CpuChipIcon className="w-4 h-4 mr-2 text-blue-500" />
                  <span className="text-gray-600 dark:text-gray-400">Modello:</span>
                  <span className="ml-1 font-medium text-gray-900 dark:text-white">
                    {metadata.model}
                  </span>
                </div>
              )}
              
              {metadata.processingTime !== undefined && (
                <div className="flex items-center text-sm">
                  <ClockIcon className="w-4 h-4 mr-2 text-green-500" />
                  <span className="text-gray-600 dark:text-gray-400">Tempo:</span>
                  <span className="ml-1 font-medium text-gray-900 dark:text-white">
                    {formatProcessingTime(metadata.processingTime)}
                  </span>
                </div>
              )}
              
              {metadata.tokens && (
                <div className="flex items-center text-sm">
                  <DocumentTextIcon className="w-4 h-4 mr-2 text-purple-500" />
                  <span className="text-gray-600 dark:text-gray-400">Token:</span>
                  <span className="ml-1 font-medium text-gray-900 dark:text-white">
                    {metadata.tokens.toLocaleString()}
                  </span>
                </div>
              )}
              
              {metadata.sources && metadata.sources.length > 0 && (
                <div className="flex items-center text-sm">
                  <DocumentTextIcon className="w-4 h-4 mr-2 text-orange-500" />
                  <span className="text-gray-600 dark:text-gray-400">Fonti:</span>
                  <span className="ml-1 font-medium text-gray-900 dark:text-white">
                    {metadata.sources.length}
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Sources */}
          {metadata?.sources && metadata.sources.length > 0 && (
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center">
                <DocumentTextIcon className="w-4 h-4 mr-1" />
                Documenti consultati
              </h4>
              <ul className="space-y-1">
                {metadata.sources.map((source, index) => (
                  <li key={index} className="text-sm text-blue-800 dark:text-blue-200">
                    • {source}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Reasoning steps */}
          {reasoning && (
            <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <h4 className="text-sm font-medium text-yellow-900 dark:text-yellow-100 mb-2 flex items-center">
                <LightBulbIcon className="w-4 h-4 mr-1" />
                Passaggi del ragionamento
              </h4>
              <div className="text-sm text-yellow-800 dark:text-yellow-200 whitespace-pre-wrap">
                {reasoning}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ReasoningDisplay;
