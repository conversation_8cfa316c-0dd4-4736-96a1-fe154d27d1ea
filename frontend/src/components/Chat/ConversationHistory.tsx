import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import {
  ChatBubbleLeftRightIcon,
  TrashIcon,
  PlusIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

// Services
import apiService from '../../services/apiService';

// Types
import { ConversationSummary, User } from '../../types';

interface ConversationHistoryProps {
  user: User | null;
  currentConversationId: string | null;
  onConversationSelect: (conversationId: string) => void;
  onNewConversation: () => void;
}

const ConversationHistory: React.FC<ConversationHistoryProps> = ({
  user,
  currentConversationId,
  onConversationSelect,
  onNewConversation
}) => {
  const [conversations, setConversations] = useState<ConversationSummary[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);

  // Load conversations when component mounts or user changes
  useEffect(() => {
    if (user) {
      loadConversations();
    }
  }, [user]);

  /**
   * Load user conversations
   */
  const loadConversations = async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      const userConversations = await apiService.getUserConversations(user.id, 50);
      setConversations(userConversations);
    } catch (error) {
      console.error('Failed to load conversations:', error);
      setError('Errore nel caricamento delle conversazioni');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Delete a conversation
   */
  const deleteConversation = async (conversationId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent conversation selection

    if (!window.confirm('Sei sicuro di voler eliminare questa conversazione?')) {
      return;
    }

    try {
      await apiService.deleteConversation(conversationId);
      
      // Remove from local state
      setConversations(prev => prev.filter(conv => conv.id !== conversationId));
      
      // If this was the current conversation, create a new one
      if (conversationId === currentConversationId) {
        onNewConversation();
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error);
      setError('Errore nell\'eliminazione della conversazione');
    }
  };

  /**
   * Generate conversation title from last message
   */
  const getConversationTitle = (conversation: ConversationSummary): string => {
    if (conversation.lastMessage) {
      // Take first 30 characters of the last message
      return conversation.lastMessage.length > 30 
        ? conversation.lastMessage.substring(0, 30) + '...'
        : conversation.lastMessage;
    }
    return `Conversazione ${format(new Date(conversation.created), 'dd/MM/yyyy', { locale: it })}`;
  };

  /**
   * Filter conversations based on search query
   */
  const filteredConversations = conversations.filter(conversation =>
    getConversationTitle(conversation).toLowerCase().includes(searchQuery.toLowerCase()) ||
    conversation.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (!user) {
    return (
      <div className="p-4 text-center text-gray-500 dark:text-gray-400">
        <ChatBubbleLeftRightIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
        <p className="text-sm">Accedi per vedere le conversazioni</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header with new conversation button */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <button
          onClick={onNewConversation}
          className="w-full flex items-center justify-center px-3 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors duration-200"
        >
          <PlusIcon className="w-4 h-4 mr-2" />
          Nuova Conversazione
        </button>
      </div>

      {/* Search */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Cerca conversazioni..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Conversations list */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="p-4 text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Caricamento...</p>
          </div>
        ) : error ? (
          <div className="p-4 text-center text-red-600 dark:text-red-400">
            <p className="text-sm">{error}</p>
            <button
              onClick={loadConversations}
              className="mt-2 text-xs text-primary-600 hover:text-primary-700 dark:text-primary-400"
            >
              Riprova
            </button>
          </div>
        ) : filteredConversations.length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            <ChatBubbleLeftRightIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">
              {searchQuery ? 'Nessuna conversazione trovata' : 'Nessuna conversazione'}
            </p>
          </div>
        ) : (
          <div className="space-y-1 p-2">
            {filteredConversations.map((conversation) => (
              <div
                key={conversation.id}
                onClick={() => onConversationSelect(conversation.id)}
                className={`
                  group relative p-3 rounded-lg cursor-pointer transition-colors duration-200
                  ${conversation.id === currentConversationId
                    ? 'bg-primary-100 dark:bg-primary-900/30 border border-primary-200 dark:border-primary-800'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                  }
                `}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {getConversationTitle(conversation)}
                    </h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {format(new Date(conversation.lastActivity), 'dd/MM/yyyy HH:mm', { locale: it })}
                    </p>
                    <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                      {conversation.messageCount} messaggi
                    </p>
                  </div>
                  
                  {/* Delete button */}
                  <button
                    onClick={(e) => deleteConversation(conversation.id, e)}
                    className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-all duration-200"
                    title="Elimina conversazione"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversationHistory;
