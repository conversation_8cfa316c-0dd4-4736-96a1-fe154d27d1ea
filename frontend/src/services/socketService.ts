import { io, Socket } from 'socket.io-client';
import { SocketEvents, User, SendMessageRequest, SendMessageResponse, Message, Conversation, RAGInfo, FileUploadProgress } from '../types';

class SocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;

  // Event listeners
  private eventListeners: Map<string, Set<Function>> = new Map();

  constructor() {
    this.connect();
  }

  /**
   * Connect to Socket.IO server
   */
  connect(): void {
    if (this.isConnecting || (this.socket && this.socket.connected)) {
      return;
    }

    this.isConnecting = true;
    const socketUrl = process.env.REACT_APP_SOCKET_URL || 'http://localhost:3001';

    this.socket = io(socketUrl, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
    });

    this.setupEventHandlers();
  }

  /**
   * Disconnect from Socket.IO server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Setup Socket.IO event handlers
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to server');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.emit('connection:status', { connected: true });
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from server:', reason);
      this.emit('connection:status', { connected: false, reason });
      
      // Auto-reconnect if disconnection was not intentional
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect
        return;
      }
      
      this.handleReconnection();
    });

    this.socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      this.isConnecting = false;
      this.emit('connection:error', { error: error.message });
      this.handleReconnection();
    });

    // User events
    this.socket.on('user:identified', (data: { user: User }) => {
      this.emit('user:identified', data);
    });

    // Chat events
    this.socket.on('chat:response', (response: SendMessageResponse) => {
      this.emit('chat:response', response);
    });

    this.socket.on('chat:error', (error: { conversationId: string; error: string; details?: string }) => {
      this.emit('chat:error', error);
    });

    this.socket.on('chat:bot_typing', (data: { conversationId: string }) => {
      this.emit('chat:bot_typing', data);
    });

    this.socket.on('chat:bot_stopped_typing', (data: { conversationId: string }) => {
      this.emit('chat:bot_stopped_typing', data);
    });

    this.socket.on('chat:user_typing', (data: { userId: string; userName: string; conversationId: string }) => {
      this.emit('chat:user_typing', data);
    });

    this.socket.on('chat:user_stopped_typing', (data: { userId: string; conversationId: string }) => {
      this.emit('chat:user_stopped_typing', data);
    });

    this.socket.on('chat:new_message', (data: { conversationId: string; message: Message }) => {
      this.emit('chat:new_message', data);
    });

    // Conversation events
    this.socket.on('conversation:joined', (data: { conversationId: string }) => {
      this.emit('conversation:joined', data);
    });

    this.socket.on('conversation:left', (data: { conversationId: string }) => {
      this.emit('conversation:left', data);
    });

    this.socket.on('conversation:history', (history: Conversation) => {
      this.emit('conversation:history', history);
    });

    this.socket.on('conversation:user_joined', (data: { userId: string; userName: string; conversationId: string }) => {
      this.emit('conversation:user_joined', data);
    });

    this.socket.on('conversation:user_left', (data: { userId: string; conversationId: string }) => {
      this.emit('conversation:user_left', data);
    });

    // File events
    this.socket.on('file:upload_progress', (data: FileUploadProgress) => {
      this.emit('file:upload_progress', data);
    });

    // RAG events
    this.socket.on('rag:status_update', (data: RAGInfo) => {
      this.emit('rag:status_update', data);
    });

    // Error events
    this.socket.on('error', (error: { message: string }) => {
      this.emit('socket:error', error);
    });
  }

  /**
   * Handle reconnection logic
   */
  private handleReconnection(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      this.emit('connection:failed', { attempts: this.reconnectAttempts });
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      if (!this.socket?.connected) {
        this.connect();
      }
    }, delay);
  }

  /**
   * Check if socket is connected
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Get socket ID
   */
  getSocketId(): string | undefined {
    return this.socket?.id;
  }

  // User methods
  identifyUser(userData: { userId?: string; name?: string }): void {
    this.socket?.emit('user:identify', userData);
  }

  // Chat methods
  sendMessage(messageData: SendMessageRequest): void {
    this.socket?.emit('chat:message', messageData);
  }

  startTyping(conversationId: string): void {
    this.socket?.emit('chat:typing', { conversationId });
  }

  stopTyping(conversationId: string): void {
    this.socket?.emit('chat:stop_typing', { conversationId });
  }

  // Conversation methods
  joinConversation(conversationId: string): void {
    this.socket?.emit('conversation:join', conversationId);
  }

  leaveConversation(conversationId: string): void {
    this.socket?.emit('conversation:leave', conversationId);
  }

  getConversationHistory(conversationId: string, limit?: number): void {
    this.socket?.emit('conversation:history', { conversationId, limit });
  }

  // RAG methods
  getRAGStatus(ragId: string): void {
    this.socket?.emit('rag:status', ragId);
  }

  // Event listener management
  on<K extends keyof SocketEvents>(event: K, callback: SocketEvents[K]): void;
  on(event: string, callback: Function): void;
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  off<K extends keyof SocketEvents>(event: K, callback: SocketEvents[K]): void;
  off(event: string, callback: Function): void;
  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
      if (listeners.size === 0) {
        this.eventListeners.delete(event);
      }
    }
  }

  /**
   * Emit event to internal listeners
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Remove all event listeners
   */
  removeAllListeners(): void {
    this.eventListeners.clear();
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): {
    connected: boolean;
    reconnectAttempts: number;
    socketId?: string;
  } {
    return {
      connected: this.isConnected(),
      reconnectAttempts: this.reconnectAttempts,
      socketId: this.getSocketId(),
    };
  }

  /**
   * Force reconnection
   */
  forceReconnect(): void {
    this.disconnect();
    this.reconnectAttempts = 0;
    setTimeout(() => this.connect(), 1000);
  }
}

// Create singleton instance
export const socketService = new SocketService();
export default socketService;