import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  ApiResponse,
  SendMessageRequest,
  SendMessageResponse,
  Conversation,
  ConversationSummary,
  FileInfo,
  UploadedFile,
  RAGInfo,
  RAGPreview,
  ChatStatistics,
  FileStatistics,
  SearchRequest,
  SearchResponse
} from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      (error) => {
        // Handle common errors
        if (error.response?.status === 401) {
          // Handle unauthorized
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Chat API methods
  async sendMessage(messageData: SendMessageRequest): Promise<SendMessageResponse> {
    const response = await this.api.post<ApiResponse<SendMessageResponse>>('/chat/message', messageData);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to send message');
    }
    return response.data.data!;
  }

  async getConversationHistory(conversationId: string, limit?: number): Promise<Conversation> {
    const response = await this.api.get<ApiResponse<Conversation>>(
      `/chat/history/${conversationId}${limit ? `?limit=${limit}` : ''}`
    );
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to get conversation history');
    }
    return response.data.data!;
  }

  async deleteConversation(conversationId: string): Promise<void> {
    const response = await this.api.delete<ApiResponse>(`/chat/conversation/${conversationId}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to delete conversation');
    }
  }

  async getUserConversations(userId?: string, limit?: number): Promise<ConversationSummary[]> {
    const params = new URLSearchParams();
    if (userId) params.append('userId', userId);
    if (limit) params.append('limit', limit.toString());

    const response = await this.api.get<ApiResponse<{ conversations: ConversationSummary[] }>>(
      `/chat/conversations?${params.toString()}`
    );
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to get conversations');
    }
    return response.data.data!.conversations;
  }

  async searchConversations(searchData: SearchRequest): Promise<SearchResponse> {
    const response = await this.api.post<ApiResponse<SearchResponse>>('/chat/search', searchData);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to search conversations');
    }
    return response.data.data!;
  }

  async getChatStatistics(): Promise<ChatStatistics> {
    const response = await this.api.get<ApiResponse<ChatStatistics>>('/chat/statistics');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to get chat statistics');
    }
    return response.data.data!;
  }

  async cleanupOldConversations(maxAge?: number): Promise<{ cleanedCount: number; maxAge: number }> {
    const response = await this.api.post<ApiResponse<{ cleanedCount: number; maxAge: number }>>(
      '/chat/cleanup',
      { maxAge }
    );
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to cleanup conversations');
    }
    return response.data.data!;
  }

  // File API methods
  async getFilesList(): Promise<FileInfo[]> {
    const response = await this.api.get<ApiResponse<{ files: FileInfo[] }>>('/files/list');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to get files list');
    }
    return response.data.data!.files;
  }

  async uploadFile(file: File, onProgress?: (progress: number) => void): Promise<UploadedFile> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.api.post<ApiResponse<UploadedFile>>('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to upload file');
    }
    return response.data.data!;
  }

  async uploadMultipleFiles(
    files: File[],
    onProgress?: (fileIndex: number, progress: number) => void
  ): Promise<{ uploaded: UploadedFile[]; errors: Array<{ filename: string; error: string }> }> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });

    const response = await this.api.post<ApiResponse<{
      uploaded: UploadedFile[];
      errors: Array<{ filename: string; error: string }>;
    }>>('/files/upload/multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          // For multiple files, we'll just report overall progress
          onProgress(0, progress);
        }
      },
    });

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to upload files');
    }
    return response.data.data!;
  }

  async getFileContent(fileId: string): Promise<{
    fileId: string;
    filename: string;
    content: string;
    contentLength: number;
    extractedAt: string;
  }> {
    const response = await this.api.get<ApiResponse<{
      fileId: string;
      filename: string;
      content: string;
      contentLength: number;
      extractedAt: string;
    }>>(`/files/content/${fileId}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to get file content');
    }
    return response.data.data!;
  }

  async deleteFile(filePath: string): Promise<void> {
    const response = await this.api.delete<ApiResponse>('/files/delete', {
      data: { filePath }
    });
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to delete file');
    }
  }

  async processFilesForRAG(): Promise<{ documents: any[]; totalDocuments: number; processedAt: string }> {
    const response = await this.api.post<ApiResponse<{
      documents: any[];
      totalDocuments: number;
      processedAt: string;
    }>>('/files/process');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to process files');
    }
    return response.data.data!;
  }

  async getFileStatistics(): Promise<FileStatistics> {
    const response = await this.api.get<ApiResponse<FileStatistics>>('/files/statistics');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to get file statistics');
    }
    return response.data.data!;
  }

  async startFileWatching(): Promise<void> {
    const response = await this.api.post<ApiResponse>('/files/watch/start');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to start file watching');
    }
  }

  async stopFileWatching(): Promise<void> {
    const response = await this.api.post<ApiResponse>('/files/watch/stop');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to stop file watching');
    }
  }

  // RAG API methods
  async createRAG(ragData: {
    name: string;
    description?: string;
    chunkSize?: number;
    overlap?: number;
    model?: string;
  }): Promise<RAGInfo> {
    const response = await this.api.post<ApiResponse<RAGInfo>>('/rag/create', ragData);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to create RAG');
    }
    return response.data.data!;
  }

  async updateRAG(ragData: {
    ragId: string;
    name?: string;
    description?: string;
    documents?: any[];
  }): Promise<RAGInfo> {
    const response = await this.api.put<ApiResponse<RAGInfo>>('/rag/update', ragData);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to update RAG');
    }
    return response.data.data!;
  }

  async deleteRAG(ragId: string): Promise<void> {
    const response = await this.api.delete<ApiResponse>(`/rag/${ragId}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to delete RAG');
    }
  }

  async getRAGStatus(ragId: string): Promise<RAGInfo> {
    const response = await this.api.get<ApiResponse<RAGInfo>>(`/rag/status/${ragId}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to get RAG status');
    }
    return response.data.data!;
  }

  async recreateRAG(ragId: string): Promise<{
    ragId: string;
    documentsCount: number;
    previousDocumentsCount: number;
    recreatedAt: string;
    status: string;
  }> {
    const response = await this.api.post<ApiResponse<{
      ragId: string;
      documentsCount: number;
      previousDocumentsCount: number;
      recreatedAt: string;
      status: string;
    }>>(`/rag/recreate/${ragId}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to recreate RAG');
    }
    return response.data.data!;
  }

  async testStraicoConnection(): Promise<{ connected: boolean; testedAt: string }> {
    const response = await this.api.post<ApiResponse<{ connected: boolean; testedAt: string }>>(
      '/rag/test-connection'
    );
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to test connection');
    }
    return response.data.data!;
  }

  async getRAGPreview(limit?: number): Promise<RAGPreview> {
    const response = await this.api.get<ApiResponse<RAGPreview>>(
      `/rag/preview${limit ? `?limit=${limit}` : ''}`
    );
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to get RAG preview');
    }
    return response.data.data!;
  }

  async syncRAG(ragId: string, force?: boolean): Promise<{
    ragId: string;
    documentsCount: number;
    syncedAt: string;
    status: string;
    forced: boolean;
  }> {
    const response = await this.api.post<ApiResponse<{
      ragId: string;
      documentsCount: number;
      syncedAt: string;
      status: string;
      forced: boolean;
    }>>(`/rag/sync/${ragId}`, { force });
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to sync RAG');
    }
    return response.data.data!;
  }

  // Configuration
  async getConfig(): Promise<{ ragId: string | null; maxFileSize: string; environment: string }> {
    const response = await this.api.get<{ ragId: string | null; maxFileSize: string; environment: string }>('/config');
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string; uptime: number }> {
    const response = await this.api.get<{ status: string; timestamp: string; uptime: number }>('/health');
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;