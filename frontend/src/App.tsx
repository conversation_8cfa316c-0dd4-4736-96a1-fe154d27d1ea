import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';

// Components
import Layout from './components/Common/Layout';
import ChatInterface from './components/Chat/ChatInterface';
import LoadingSpinner from './components/Common/LoadingSpinner';

// Services
import socketService from './services/socketService';

// Types
import { User, AppState, Theme } from './types';

// Hooks
import { useLocalStorage } from './hooks/useLocalStorage';

function App() {
  // State management
  const [appState, setAppState] = useState<AppState>({
    user: null,
    isConnected: false,
    theme: 'system',
    sidebarOpen: true,
    currentView: 'chat'
  });

  const [isLoading, setIsLoading] = useState(true);
  const [theme, setTheme] = useLocalStorage<Theme>('theme', 'system');
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);

  // Initialize app
  useEffect(() => {
    initializeApp();
    setupSocketListeners();
    
    return () => {
      socketService.removeAllListeners();
    };
  }, []);

  // Theme management
  useEffect(() => {
    applyTheme(theme);
    setAppState(prev => ({ ...prev, theme }));
  }, [theme]);

  /**
   * Initialize the application
   */
  const initializeApp = async () => {
    try {
      // Get user from localStorage if available
      const savedUser = localStorage.getItem('user');
      if (savedUser) {
        const user: User = JSON.parse(savedUser);
        setAppState(prev => ({ ...prev, user }));
        
        // Identify user with socket service
        socketService.identifyUser({
          userId: user.id,
          name: user.name
        });
      } else {
        // Create anonymous user
        const anonymousUser: User = {
          id: `anonymous_${Date.now()}`,
          name: 'Utente Anonimo',
          socketId: '',
          connectedAt: new Date().toISOString()
        };
        
        setAppState(prev => ({ ...prev, user: anonymousUser }));
        localStorage.setItem('user', JSON.stringify(anonymousUser));
        
        socketService.identifyUser({
          userId: anonymousUser.id,
          name: anonymousUser.name
        });
      }
    } catch (error) {
      console.error('Failed to initialize app:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Setup Socket.IO event listeners
   */
  const setupSocketListeners = () => {
    // Connection status
    socketService.on('connection:status', ({ connected }: { connected: boolean }) => {
      setAppState(prev => ({ ...prev, isConnected: connected }));
    });

    // User identification
    socketService.on('user:identified', ({ user }: { user: User }) => {
      setAppState(prev => ({ ...prev, user }));
      localStorage.setItem('user', JSON.stringify(user));
    });

    // Connection errors
    socketService.on('connection:error', ({ error }: { error: string }) => {
      console.error('Socket connection error:', error);
    });

    // Connection failed
    socketService.on('connection:failed', ({ attempts }: { attempts: number }) => {
      console.error(`Connection failed after ${attempts} attempts`);
    });
  };

  /**
   * Apply theme to document
   */
  const applyTheme = (newTheme: Theme) => {
    const root = document.documentElement;
    
    if (newTheme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      root.classList.toggle('dark', systemTheme === 'dark');
    } else {
      root.classList.toggle('dark', newTheme === 'dark');
    }
  };

  /**
   * Toggle theme
   */
  const toggleTheme = () => {
    const themes: Theme[] = ['light', 'dark', 'system'];
    const currentIndex = themes.indexOf(theme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];
    setTheme(nextTheme);
  };

  /**
   * Toggle sidebar
   */
  const toggleSidebar = () => {
    setAppState(prev => ({ ...prev, sidebarOpen: !prev.sidebarOpen }));
  };

  /**
   * Change current view
   */
  const changeView = (view: AppState['currentView']) => {
    setAppState(prev => ({ ...prev, currentView: view }));
  };

  /**
   * Handle conversation selection
   */
  const handleConversationSelect = (conversationId: string) => {
    setCurrentConversationId(conversationId);
  };

  /**
   * Handle new conversation creation
   */
  const handleNewConversation = () => {
    setCurrentConversationId(null);
  };

  /**
   * Handle conversation change from ChatInterface
   */
  const handleConversationChange = (conversationId: string | null) => {
    setCurrentConversationId(conversationId);
  };

  // Loading screen
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            Caricamento applicazione...
          </p>
        </div>
      </div>
    );
  }

  return (
    <Router>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Layout
          user={appState.user}
          isConnected={appState.isConnected}
          theme={theme}
          sidebarOpen={appState.sidebarOpen}
          currentView={appState.currentView}
          onToggleTheme={toggleTheme}
          onToggleSidebar={toggleSidebar}
          onChangeView={changeView}
          currentConversationId={currentConversationId}
          onConversationSelect={handleConversationSelect}
          onNewConversation={handleNewConversation}
        >
          <Routes>
            {/* Main chat interface */}
            <Route
              path="/"
              element={
                <ChatInterface
                  user={appState.user}
                  isConnected={appState.isConnected}
                  currentConversationId={currentConversationId}
                  onConversationChange={handleConversationChange}
                />
              }
            />

            {/* Redirect unknown routes to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Layout>

        {/* Toast notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'var(--toast-bg)',
              color: 'var(--toast-color)',
              border: '1px solid var(--toast-border)',
            },
            success: {
              iconTheme: {
                primary: '#10b981',
                secondary: '#ffffff',
              },
            },
            error: {
              iconTheme: {
                primary: '#ef4444',
                secondary: '#ffffff',
              },
            },
          }}
        />
      </div>
    </Router>
  );
}

export default App;