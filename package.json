{"name": "chatbot-straico", "version": "1.0.0", "description": "RAG-based chatbot using Straico APIs with Node.js backend and React frontend", "main": "backend/src/server.js", "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\"", "backend:dev": "cd backend && npm run dev", "frontend:dev": "cd frontend && npm start", "backend:install": "cd backend && npm install", "frontend:install": "cd frontend && npm install", "install:all": "npm run backend:install && npm run frontend:install", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "test": "npm run backend:test && npm run frontend:test", "backend:test": "cd backend && npm test", "frontend:test": "cd frontend && npm test", "create-rag": "cd backend && npm run create-rag"}, "keywords": ["chatbot", "rag", "straico", "nodejs", "react"], "author": "Softway", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}