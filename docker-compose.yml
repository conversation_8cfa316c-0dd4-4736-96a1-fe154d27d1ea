version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - STRAICO_API_KEY=${STRAICO_API_KEY}
      - STRAICO_BASE_URL=${STRAICO_BASE_URL:-https://api.straico.com}
      - CORS_ORIGIN=http://localhost:3000
      - BASE_DIRECTORY=/app/base
      - UPLOAD_DIRECTORY=/app/uploads
    volumes:
      - ./base:/app/base:ro
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:3001/api
      - REACT_APP_SOCKET_URL=http://localhost:3001
    depends_on:
      - backend
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  backend_uploads:
  backend_logs:
  redis_data: