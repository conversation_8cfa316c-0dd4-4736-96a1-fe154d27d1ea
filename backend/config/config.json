{"straico": {"apiKey": "STRAICO_API_KEY", "baseUrl": "https://api.straico.com", "ragEndpoint": "/v0/rag", "chatEndpoint": "/v0/rag"}, "server": {"port": 3001, "corsOrigin": "http://localhost:3000", "maxRequestSize": "50mb"}, "files": {"baseDirectory": "../base", "maxFileSize": "10MB", "allowedFormats": ["pdf", "txt", "doc", "docx", "md", "html"], "uploadDirectory": "./uploads"}, "rag": {"chunkSize": 1000, "overlap": 200, "model": "openai/gpt-4o-mini", "maxTokens": 4000, "temperature": 0.7}, "logging": {"level": "info", "file": "./logs/app.log", "maxFiles": 5, "maxSize": "10m"}, "rateLimit": {"windowMs": 900000, "max": 100}}