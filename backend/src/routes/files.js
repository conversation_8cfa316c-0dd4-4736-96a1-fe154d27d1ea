const express = require('express');
const multer = require('multer');
const Joi = require('joi');
const fileService = require('../services/fileService');
const logger = require('../utils/logger');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['application/pdf', 'text/plain', 'text/markdown', 'text/html', 
                         'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('File type not allowed'), false);
    }
  }
});

// Validation schemas
const deleteFileSchema = Joi.object({
  filePath: Joi.string().required()
});

/**
 * GET /api/files/list
 * Get list of all files
 */
router.get('/list', async (req, res) => {
  try {
    const files = await fileService.getAllFiles();
    
    res.json({
      success: true,
      data: {
        files,
        total: files.length
      }
    });
  } catch (error) {
    logger.error('Get files list endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get files list',
      message: error.message
    });
  }
});

/**
 * POST /api/files/upload
 * Upload a new file
 */
router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file provided'
      });
    }

    const fileInfo = await fileService.saveUploadedFile(req.file);
    
    res.json({
      success: true,
      data: fileInfo,
      message: 'File uploaded successfully'
    });
  } catch (error) {
    logger.error('File upload endpoint error:', error);
    
    if (error.message.includes('File format') || error.message.includes('File size')) {
      return res.status(400).json({
        success: false,
        error: error.message
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to upload file',
      message: error.message
    });
  }
});

/**
 * POST /api/files/upload/multiple
 * Upload multiple files
 */
router.post('/upload/multiple', upload.array('files', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No files provided'
      });
    }

    const uploadResults = [];
    const errors = [];

    for (const file of req.files) {
      try {
        const fileInfo = await fileService.saveUploadedFile(file);
        uploadResults.push(fileInfo);
      } catch (error) {
        errors.push({
          filename: file.originalname,
          error: error.message
        });
      }
    }

    res.json({
      success: true,
      data: {
        uploaded: uploadResults,
        errors: errors,
        totalUploaded: uploadResults.length,
        totalErrors: errors.length
      }
    });
  } catch (error) {
    logger.error('Multiple file upload endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to upload files',
      message: error.message
    });
  }
});

/**
 * GET /api/files/content/:fileId
 * Get file content by ID
 */
router.get('/content/:fileId', async (req, res) => {
  try {
    const { fileId } = req.params;
    
    // First get the file list to find the file by ID
    const files = await fileService.getAllFiles();
    const file = files.find(f => f.id === fileId);
    
    if (!file) {
      return res.status(404).json({
        success: false,
        error: 'File not found'
      });
    }

    const content = await fileService.extractTextFromFile(file.path);
    
    res.json({
      success: true,
      data: {
        fileId,
        filename: file.name,
        content,
        contentLength: content.length,
        extractedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Get file content endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get file content',
      message: error.message
    });
  }
});

/**
 * DELETE /api/files/delete
 * Delete a file
 */
router.delete('/delete', validateRequest(deleteFileSchema), async (req, res) => {
  try {
    const { filePath } = req.body;
    
    const deleted = await fileService.deleteFile(filePath);
    
    if (!deleted) {
      return res.status(404).json({
        success: false,
        error: 'File not found or could not be deleted'
      });
    }

    res.json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    logger.error('Delete file endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete file',
      message: error.message
    });
  }
});

/**
 * POST /api/files/process
 * Process all files for RAG
 */
router.post('/process', async (req, res) => {
  try {
    const documents = await fileService.processAllFilesForRAG();
    
    res.json({
      success: true,
      data: {
        documents,
        totalDocuments: documents.length,
        processedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Process files endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process files',
      message: error.message
    });
  }
});

/**
 * GET /api/files/statistics
 * Get file statistics
 */
router.get('/statistics', async (req, res) => {
  try {
    const files = await fileService.getAllFiles();
    
    const stats = {
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      fileTypes: {},
      lastModified: null
    };

    // Calculate file type distribution
    files.forEach(file => {
      stats.fileTypes[file.type] = (stats.fileTypes[file.type] || 0) + 1;
      
      if (!stats.lastModified || new Date(file.lastModified) > new Date(stats.lastModified)) {
        stats.lastModified = file.lastModified;
      }
    });

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('File statistics endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get file statistics',
      message: error.message
    });
  }
});

/**
 * POST /api/files/watch/start
 * Start watching base directory for changes
 */
router.post('/watch/start', (req, res) => {
  try {
    fileService.startWatching((event, filePath) => {
      logger.info(`File ${event}: ${filePath}`);
      // Here you could emit socket events to notify clients
    });

    res.json({
      success: true,
      message: 'File watching started'
    });
  } catch (error) {
    logger.error('Start file watching endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to start file watching',
      message: error.message
    });
  }
});

/**
 * POST /api/files/watch/stop
 * Stop watching base directory
 */
router.post('/watch/stop', (req, res) => {
  try {
    fileService.stopWatching();

    res.json({
      success: true,
      message: 'File watching stopped'
    });
  } catch (error) {
    logger.error('Stop file watching endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to stop file watching',
      message: error.message
    });
  }
});

module.exports = router;