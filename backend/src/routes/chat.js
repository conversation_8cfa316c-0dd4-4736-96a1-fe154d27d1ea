const express = require('express');
const Joi = require('joi');
const chatService = require('../services/chatService');
const logger = require('../utils/logger');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Validation schemas
const messageSchema = Joi.object({
  message: Joi.string().required().min(1).max(5000),
  conversationId: Joi.string().optional(),
  userId: Joi.string().optional(),
  ragId: Joi.string().optional(),
  systemPrompt: Joi.string().optional().max(2000)
});

const historySchema = Joi.object({
  conversationId: Joi.string().required(),
  limit: Joi.number().optional().min(1).max(100).default(50)
});

const searchSchema = Joi.object({
  query: Joi.string().required().min(1).max(200),
  userId: Joi.string().optional()
});

/**
 * POST /api/chat/message
 * Send a chat message
 */
router.post('/message', validateRequest(messageSchema), async (req, res) => {
  try {
    const response = await chatService.processMessage(req.body);
    
    res.json({
      success: true,
      data: response
    });
  } catch (error) {
    logger.error('Chat message endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process message',
      message: error.message
    });
  }
});

/**
 * GET /api/chat/history/:conversationId
 * Get conversation history
 */
router.get('/history/:conversationId', async (req, res) => {
  try {
    const { conversationId } = req.params;
    const limit = parseInt(req.query.limit) || 50;

    // Validate parameters
    const { error } = historySchema.validate({ conversationId, limit });
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.details
      });
    }

    const history = chatService.getConversationHistory(conversationId, limit);
    
    res.json({
      success: true,
      data: history
    });
  } catch (error) {
    logger.error('Chat history endpoint error:', error);
    
    if (error.message === 'Conversation not found') {
      return res.status(404).json({
        success: false,
        error: 'Conversation not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to get conversation history',
      message: error.message
    });
  }
});

/**
 * DELETE /api/chat/conversation/:conversationId
 * Delete a conversation
 */
router.delete('/conversation/:conversationId', async (req, res) => {
  try {
    const { conversationId } = req.params;
    
    const deleted = chatService.deleteConversation(conversationId);
    
    if (!deleted) {
      return res.status(404).json({
        success: false,
        error: 'Conversation not found'
      });
    }

    res.json({
      success: true,
      message: 'Conversation deleted successfully'
    });
  } catch (error) {
    logger.error('Delete conversation endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete conversation',
      message: error.message
    });
  }
});

/**
 * GET /api/chat/conversations
 * Get user conversations
 */
router.get('/conversations', async (req, res) => {
  try {
    const userId = req.query.userId || 'anonymous';
    const limit = parseInt(req.query.limit) || 20;

    const conversations = chatService.getUserConversations(userId, limit);
    
    res.json({
      success: true,
      data: {
        conversations,
        total: conversations.length
      }
    });
  } catch (error) {
    logger.error('Get conversations endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get conversations',
      message: error.message
    });
  }
});

/**
 * POST /api/chat/search
 * Search conversations
 */
router.post('/search', validateRequest(searchSchema), async (req, res) => {
  try {
    const { query, userId } = req.body;
    
    const results = chatService.searchConversations(query, userId);
    
    res.json({
      success: true,
      data: {
        results,
        total: results.length,
        query
      }
    });
  } catch (error) {
    logger.error('Search conversations endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to search conversations',
      message: error.message
    });
  }
});

/**
 * GET /api/chat/statistics
 * Get chat statistics
 */
router.get('/statistics', async (req, res) => {
  try {
    const stats = chatService.getStatistics();
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Chat statistics endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get statistics',
      message: error.message
    });
  }
});

/**
 * POST /api/chat/cleanup
 * Clean up old conversations
 */
router.post('/cleanup', async (req, res) => {
  try {
    const maxAge = parseInt(req.body.maxAge) || 30;
    
    const cleanedCount = chatService.cleanupOldConversations(maxAge);
    
    res.json({
      success: true,
      data: {
        cleanedCount,
        maxAge
      }
    });
  } catch (error) {
    logger.error('Chat cleanup endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cleanup conversations',
      message: error.message
    });
  }
});

module.exports = router;