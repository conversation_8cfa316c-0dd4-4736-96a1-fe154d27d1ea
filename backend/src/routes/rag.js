const express = require('express');
const Joi = require('joi');
const straicoService = require('../services/straicoService');
const fileService = require('../services/fileService');
const logger = require('../utils/logger');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Validation schemas
const createRAGSchema = Joi.object({
  name: Joi.string().required().min(1).max(100),
  description: Joi.string().optional().max(500),
  chunkSize: Joi.number().optional().min(100).max(5000),
  overlap: Joi.number().optional().min(0).max(1000),
  model: Joi.string().optional()
});

const updateRAGSchema = Joi.object({
  ragId: Joi.string().required(),
  name: Joi.string().optional().min(1).max(100),
  description: Joi.string().optional().max(500),
  documents: Joi.array().optional()
});

/**
 * POST /api/rag/create
 * Create a new RAG knowledge base
 */
router.post('/create', validateRequest(createRAGSchema), async (req, res) => {
  try {
    const { name, description, chunkSize, overlap, model } = req.body;

    // Process all files to get documents
    logger.info('Processing files for RAG creation...');
    const documents = await fileService.processAllFilesForRAG();

    if (documents.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No documents found to create RAG',
        message: 'Please ensure there are supported files in the base directory'
      });
    }

    // Create RAG with Straico
    const ragData = {
      name,
      description: description || `RAG knowledge base created from ${documents.length} documents`,
      documents,
      chunkSize,
      overlap,
      model
    };

    const ragResponse = await straicoService.createRAG(ragData);

    res.json({
      success: true,
      data: {
        ragId: ragResponse.id,
        name: ragResponse.name,
        description: ragResponse.description,
        documentsCount: documents.length,
        status: ragResponse.status,
        createdAt: ragResponse.createdAt || new Date().toISOString()
      },
      message: 'RAG created successfully'
    });
  } catch (error) {
    logger.error('Create RAG endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create RAG',
      message: error.message
    });
  }
});

/**
 * PUT /api/rag/update
 * Update an existing RAG knowledge base
 */
router.put('/update', validateRequest(updateRAGSchema), async (req, res) => {
  try {
    const { ragId, ...updateData } = req.body;

    // If no documents provided, process all files
    if (!updateData.documents) {
      logger.info('Processing files for RAG update...');
      updateData.documents = await fileService.processAllFilesForRAG();
    }

    const ragResponse = await straicoService.updateRAG(ragId, updateData);

    res.json({
      success: true,
      data: ragResponse,
      message: 'RAG updated successfully'
    });
  } catch (error) {
    logger.error('Update RAG endpoint error:', error);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'RAG not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to update RAG',
      message: error.message
    });
  }
});

/**
 * DELETE /api/rag/:ragId
 * Delete a RAG knowledge base
 */
router.delete('/:ragId', async (req, res) => {
  try {
    const { ragId } = req.params;

    await straicoService.deleteRAG(ragId);

    res.json({
      success: true,
      message: 'RAG deleted successfully'
    });
  } catch (error) {
    logger.error('Delete RAG endpoint error:', error);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'RAG not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to delete RAG',
      message: error.message
    });
  }
});

/**
 * GET /api/rag/status/:ragId
 * Get RAG status and information
 */
router.get('/status/:ragId', async (req, res) => {
  try {
    const { ragId } = req.params;

    const ragStatus = await straicoService.getRAGStatus(ragId);

    res.json({
      success: true,
      data: ragStatus
    });
  } catch (error) {
    logger.error('Get RAG status endpoint error:', error);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'RAG not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to get RAG status',
      message: error.message
    });
  }
});

/**
 * POST /api/rag/recreate/:ragId
 * Recreate RAG with current files
 */
router.post('/recreate/:ragId', async (req, res) => {
  try {
    const { ragId } = req.params;

    // Get current RAG info
    const currentRAG = await straicoService.getRAGStatus(ragId);
    
    // Process all files
    logger.info('Processing files for RAG recreation...');
    const documents = await fileService.processAllFilesForRAG();

    if (documents.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No documents found to recreate RAG'
      });
    }

    // Update RAG with new documents
    const updateData = {
      documents,
      description: `RAG recreated with ${documents.length} documents at ${new Date().toISOString()}`
    };

    const ragResponse = await straicoService.updateRAG(ragId, updateData);

    res.json({
      success: true,
      data: {
        ragId,
        documentsCount: documents.length,
        previousDocumentsCount: currentRAG.documentsCount || 0,
        recreatedAt: new Date().toISOString(),
        status: ragResponse.status
      },
      message: 'RAG recreated successfully'
    });
  } catch (error) {
    logger.error('Recreate RAG endpoint error:', error);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'RAG not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to recreate RAG',
      message: error.message
    });
  }
});

/**
 * POST /api/rag/test-connection
 * Test Straico API connection
 */
router.post('/test-connection', async (req, res) => {
  try {
    const isConnected = await straicoService.testConnection();

    res.json({
      success: true,
      data: {
        connected: isConnected,
        testedAt: new Date().toISOString()
      },
      message: isConnected ? 'Connection successful' : 'Connection failed'
    });
  } catch (error) {
    logger.error('Test connection endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test connection',
      message: error.message
    });
  }
});

/**
 * GET /api/rag/preview
 * Preview documents that would be used for RAG
 */
router.get('/preview', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    
    // Process files but limit the preview
    const documents = await fileService.processAllFilesForRAG();
    const preview = documents.slice(0, limit).map(doc => ({
      id: doc.id,
      title: doc.title,
      contentPreview: doc.content.substring(0, 200) + (doc.content.length > 200 ? '...' : ''),
      contentLength: doc.content.length,
      metadata: doc.metadata
    }));

    res.json({
      success: true,
      data: {
        preview,
        totalDocuments: documents.length,
        previewCount: preview.length,
        totalContentLength: documents.reduce((sum, doc) => sum + doc.content.length, 0)
      }
    });
  } catch (error) {
    logger.error('RAG preview endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate RAG preview',
      message: error.message
    });
  }
});

/**
 * POST /api/rag/sync
 * Sync RAG with current files (incremental update)
 */
router.post('/sync/:ragId', async (req, res) => {
  try {
    const { ragId } = req.params;
    const { force = false } = req.body;

    // Get current RAG status
    const currentRAG = await straicoService.getRAGStatus(ragId);
    
    // Process current files
    const documents = await fileService.processAllFilesForRAG();
    
    // For now, we'll do a full update
    // In a more sophisticated implementation, you would compare timestamps
    // and only update changed documents
    
    const updateData = {
      documents,
      description: `RAG synced with ${documents.length} documents at ${new Date().toISOString()}`
    };

    const ragResponse = await straicoService.updateRAG(ragId, updateData);

    res.json({
      success: true,
      data: {
        ragId,
        documentsCount: documents.length,
        syncedAt: new Date().toISOString(),
        status: ragResponse.status,
        forced: force
      },
      message: 'RAG synced successfully'
    });
  } catch (error) {
    logger.error('Sync RAG endpoint error:', error);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'RAG not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to sync RAG',
      message: error.message
    });
  }
});

module.exports = router;