const Joi = require('joi');
const logger = require('../utils/logger');

/**
 * Middleware to validate request data using Joi schemas
 * @param {Object} schema - Joi validation schema
 * @param {string} property - Property to validate ('body', 'query', 'params')
 * @returns {Function} Express middleware function
 */
const validateRequest = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false, // Include all errors
      allowUnknown: false, // Don't allow unknown fields
      stripUnknown: true // Remove unknown fields
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      logger.warn('Validation error:', {
        endpoint: req.path,
        method: req.method,
        errors: errorDetails
      });

      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: errorDetails
      });
    }

    // Replace the request property with the validated value
    req[property] = value;
    next();
  };
};

/**
 * Middleware to validate query parameters
 * @param {Object} schema - Joi validation schema
 * @returns {Function} Express middleware function
 */
const validateQuery = (schema) => {
  return validateRequest(schema, 'query');
};

/**
 * Middleware to validate URL parameters
 * @param {Object} schema - Joi validation schema
 * @returns {Function} Express middleware function
 */
const validateParams = (schema) => {
  return validateRequest(schema, 'params');
};

/**
 * Common validation schemas
 */
const commonSchemas = {
  // UUID validation
  uuid: Joi.string().uuid(),
  
  // Pagination
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    offset: Joi.number().integer().min(0)
  }),

  // Search
  search: Joi.object({
    query: Joi.string().min(1).max(200).required(),
    filters: Joi.object().optional()
  }),

  // File upload
  fileUpload: Joi.object({
    category: Joi.string().optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    description: Joi.string().max(500).optional()
  }),

  // Date range
  dateRange: Joi.object({
    startDate: Joi.date().iso(),
    endDate: Joi.date().iso().min(Joi.ref('startDate'))
  }),

  // ID parameter
  idParam: Joi.object({
    id: Joi.string().required()
  })
};

/**
 * Sanitize input to prevent XSS and other attacks
 * @param {string} input - Input string to sanitize
 * @returns {string} Sanitized string
 */
const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .replace(/[<>]/g, '') // Remove < and > characters
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

/**
 * Middleware to sanitize request body
 * @param {Array} fields - Fields to sanitize
 * @returns {Function} Express middleware function
 */
const sanitizeBody = (fields = []) => {
  return (req, res, next) => {
    if (req.body && typeof req.body === 'object') {
      const fieldsToSanitize = fields.length > 0 ? fields : Object.keys(req.body);
      
      fieldsToSanitize.forEach(field => {
        if (req.body[field] && typeof req.body[field] === 'string') {
          req.body[field] = sanitizeInput(req.body[field]);
        }
      });
    }
    next();
  };
};

/**
 * Middleware to validate file uploads
 * @param {Object} options - Upload validation options
 * @returns {Function} Express middleware function
 */
const validateFileUpload = (options = {}) => {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    allowedTypes = ['application/pdf', 'text/plain', 'text/markdown'],
    required = true
  } = options;

  return (req, res, next) => {
    if (required && (!req.file && !req.files)) {
      return res.status(400).json({
        success: false,
        error: 'File is required'
      });
    }

    const files = req.files || (req.file ? [req.file] : []);
    
    for (const file of files) {
      // Check file size
      if (file.size > maxSize) {
        return res.status(400).json({
          success: false,
          error: `File ${file.originalname} exceeds maximum size of ${maxSize} bytes`
        });
      }

      // Check file type
      if (!allowedTypes.includes(file.mimetype)) {
        return res.status(400).json({
          success: false,
          error: `File type ${file.mimetype} is not allowed`
        });
      }
    }

    next();
  };
};

/**
 * Middleware to validate API key
 * @param {string} headerName - Header name for API key
 * @returns {Function} Express middleware function
 */
const validateApiKey = (headerName = 'x-api-key') => {
  return (req, res, next) => {
    const apiKey = req.headers[headerName];
    
    if (!apiKey) {
      return res.status(401).json({
        success: false,
        error: 'API key is required'
      });
    }

    // Here you would validate the API key against your database
    // For now, we'll just check if it exists
    if (apiKey.length < 10) {
      return res.status(401).json({
        success: false,
        error: 'Invalid API key'
      });
    }

    req.apiKey = apiKey;
    next();
  };
};

/**
 * Error handler for validation middleware
 */
const validationErrorHandler = (err, req, res, next) => {
  if (err.name === 'ValidationError') {
    logger.error('Validation error:', err);
    return res.status(400).json({
      success: false,
      error: 'Validation error',
      message: err.message
    });
  }
  next(err);
};

module.exports = {
  validateRequest,
  validateQuery,
  validateParams,
  validateFileUpload,
  validateApiKey,
  sanitizeInput,
  sanitizeBody,
  validationErrorHandler,
  commonSchemas
};