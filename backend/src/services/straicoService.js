const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs-extra');
const path = require('path');
const logger = require('../utils/logger');
const config = require('../../config/config.json');

class StraicoService {
  constructor() {
    this.apiKey = process.env.STRAICO_API_KEY || config.straico.apiKey;
    this.baseUrl = process.env.STRAICO_BASE_URL || config.straico.baseUrl;
    this.ragEndpoint = config.straico.ragEndpoint;
    this.chatEndpoint = config.straico.chatEndpoint;
    
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000 // Increased timeout for file uploads
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.info(`Straico API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error('Straico API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    this.client.interceptors.response.use(
      (response) => {
        logger.info(`Straico API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        logger.error('Straico API Response Error:', {
          status: error.response?.status,
          message: error.response?.data?.message || error.message,
          url: error.config?.url
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Create a new RAG knowledge base using Straico API
   * @param {Object} ragData - The RAG configuration and data
   * @returns {Promise<Object>} RAG creation response
   */
  async createRAG(ragData) {
    try {
      const formData = new FormData();
      
      // Add RAG metadata
      formData.append('name', ragData.name);
      formData.append('description', ragData.description || '');
      formData.append('chunking_method', 'fixed_size');
      formData.append('chunk_size', ragData.chunkSize || config.rag.chunkSize);
      formData.append('chunk_overlap', ragData.overlap || config.rag.overlap);
      
      // Create temporary directory
      const tempDir = path.join(__dirname, '../../temp');
      await fs.ensureDir(tempDir);
      
      // Create a combined text file from all documents
      const combinedContent = ragData.documents.map(doc => 
        `=== ${doc.title} ===\n${doc.content}\n\n`
      ).join('');
      
      const tempFilePath = path.join(tempDir, `softway_documents_${Date.now()}.txt`);
      await fs.writeFile(tempFilePath, combinedContent, 'utf8');
      
      // Add the file to form data
      formData.append('files', fs.createReadStream(tempFilePath));

      const response = await this.client.post(this.ragEndpoint, formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${this.apiKey}`
        },
        maxContentLength: Infinity,
        maxBodyLength: Infinity
      });

      // Clean up temp file
      await fs.remove(tempFilePath).catch(() => {});

      logger.info('RAG created successfully:', response.data.data._id);
      
      return {
        id: response.data.data._id,
        ragId: response.data.data._id,
        name: response.data.data.name,
        description: response.data.data.description,
        status: 'active',
        createdAt: response.data.data.createdAt,
        documentsCount: ragData.documents.length,
        chunkSize: response.data.data.chunk_size,
        chunkOverlap: response.data.data.chunk_overlap
      };
    } catch (error) {
      logger.error('Failed to create RAG:', error.response?.data || error.message);
      throw new Error(`RAG creation failed: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Update an existing RAG knowledge base
   * @param {string} ragId - The RAG ID to update
   * @param {Object} updateData - The update data
   * @returns {Promise<Object>} RAG update response
   */
  async updateRAG(ragId, updateData) {
    try {
      const formData = new FormData();
      
      if (updateData.documents && updateData.documents.length > 0) {
        // Create temporary directory
        const tempDir = path.join(__dirname, '../../temp');
        await fs.ensureDir(tempDir);
        
        // Create a combined text file from new documents
        const combinedContent = updateData.documents.map(doc => 
          `=== ${doc.title} ===\n${doc.content}\n\n`
        ).join('');
        
        const tempFilePath = path.join(tempDir, `update_documents_${Date.now()}.txt`);
        await fs.writeFile(tempFilePath, combinedContent, 'utf8');
        
        // Add the file to form data
        formData.append('files', fs.createReadStream(tempFilePath));

        const response = await this.client.put(`${this.ragEndpoint}/${ragId}`, formData, {
          headers: {
            ...formData.getHeaders(),
            'Authorization': `Bearer ${this.apiKey}`
          },
          maxContentLength: Infinity,
          maxBodyLength: Infinity
        });

        // Clean up temp file
        await fs.remove(tempFilePath).catch(() => {});

        logger.info('RAG updated successfully:', ragId);
        return response.data;
      } else {
        throw new Error('No documents provided for update');
      }
    } catch (error) {
      logger.error('Failed to update RAG:', error.response?.data || error.message);
      throw new Error(`RAG update failed: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Delete a RAG knowledge base
   * @param {string} ragId - The RAG ID to delete
   * @returns {Promise<Object>} RAG deletion response
   */
  async deleteRAG(ragId) {
    try {
      const response = await this.client.delete(`${this.ragEndpoint}/${ragId}`);
      logger.info('RAG deleted successfully:', ragId);
      return response.data;
    } catch (error) {
      logger.error('Failed to delete RAG:', error.response?.data || error.message);
      throw new Error(`RAG deletion failed: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get RAG status and information
   * @param {string} ragId - The RAG ID
   * @returns {Promise<Object>} RAG status response
   */
  async getRAGStatus(ragId) {
    try {
      const response = await this.client.get(`${this.ragEndpoint}/${ragId}`);
      return {
        id: response.data.data._id,
        name: response.data.data.name,
        description: response.data.data.description,
        status: 'active',
        createdAt: response.data.data.createdAt,
        updatedAt: response.data.data.updatedAt,
        chunkSize: response.data.data.chunk_size,
        chunkOverlap: response.data.data.chunk_overlap,
        documentsCount: response.data.data.original_filename ? response.data.data.original_filename.split(',').length : 0
      };
    } catch (error) {
      logger.error('Failed to get RAG status:', error.response?.data || error.message);
      throw new Error(`RAG status retrieval failed: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Send a chat message using RAG context
   * @param {Object} chatData - The chat message data
   * @returns {Promise<Object>} Chat response
   */
  async sendChatMessage(chatData) {
    try {
      if (!chatData.ragId) {
        throw new Error('RAG ID is required for chat messages');
      }

      const payload = {
        prompt: chatData.message,
        model: chatData.model || config.rag.model || 'openai/gpt-4o-mini',
        // Optional RAG retrieval parameters
        search_type: 'similarity',
        k: 5, // Number of documents to retrieve
        score_threshold: 0.5
      };

      const response = await this.client.post(`${this.ragEndpoint}/${chatData.ragId}/prompt`, payload, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Bearer ${this.apiKey}`
        }
      });
      
      logger.info('Chat message processed successfully');
      
      // Extract sources from references
      const sources = response.data.data.references?.map((ref, index) => ({
        id: `source_${index}`,
        title: `${response.data.data.file_name} - Page ${ref.page}`,
        content: ref.page_content,
        metadata: {
          sourceFile: response.data.data.file_name,
          page: ref.page,
          chunkIndex: index,
          totalChunks: response.data.data.references.length
        }
      })) || [];

      return {
        response: response.data.data.answer,
        conversationId: chatData.conversationId,
        sources: sources,
        timestamp: new Date().toISOString(),
        usage: {
          totalTokens: Math.round(response.data.data.coins_used * 100), // Approximate token count
          promptTokens: 0,
          completionTokens: Math.round(response.data.data.coins_used * 100)
        }
      };
    } catch (error) {
      logger.error('Failed to send chat message:', error.response?.data || error.message);
      throw new Error(`Chat message failed: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get conversation history (not supported by Straico RAG API)
   * @param {string} conversationId - The conversation ID
   * @returns {Promise<Object>} Conversation history
   */
  async getConversationHistory(conversationId) {
    // Straico RAG API doesn't support conversation history
    // This would need to be implemented locally
    throw new Error('Conversation history not supported by Straico RAG API');
  }

  /**
   * Test API connection
   * @returns {Promise<boolean>} Connection status
   */
  async testConnection() {
    try {
      // Test with a simple API call to check if the API key is valid
      const response = await this.client.get('/v1/models');
      logger.info('Straico API connection test successful');
      return true;
    } catch (error) {
      logger.error('Straico API connection test failed:', error.message);
      return false;
    }
  }
}

module.exports = new StraicoService();