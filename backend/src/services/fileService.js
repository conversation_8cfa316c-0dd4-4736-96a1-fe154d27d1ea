const fs = require('fs-extra');
const path = require('path');
const pdfParse = require('pdf-parse');
const mammoth = require('mammoth');
const { marked } = require('marked');
const cheerio = require('cheerio');
const chokidar = require('chokidar');
const { v4: uuidv4 } = require('uuid');

const logger = require('../utils/logger');
const config = require('../../config/config.json');

class FileService {
  constructor() {
    this.baseDirectory = path.resolve(process.env.BASE_DIRECTORY || config.files.baseDirectory);
    this.uploadDirectory = path.resolve(process.env.UPLOAD_DIRECTORY || config.files.uploadDirectory);
    this.allowedFormats = config.files.allowedFormats;
    this.maxFileSize = this.parseFileSize(config.files.maxFileSize);
    this.watcher = null;
    
    this.ensureDirectories();
  }

  /**
   * Ensure required directories exist
   */
  async ensureDirectories() {
    try {
      await fs.ensureDir(this.uploadDirectory);
      await fs.ensureDir(this.baseDirectory);
      logger.info('File service directories initialized');
    } catch (error) {
      logger.error('Failed to create directories:', error);
      throw error;
    }
  }

  /**
   * Parse file size string to bytes
   * @param {string} sizeStr - Size string like "10MB"
   * @returns {number} Size in bytes
   */
  parseFileSize(sizeStr) {
    const units = { B: 1, KB: 1024, MB: 1024 * 1024, GB: 1024 * 1024 * 1024 };
    const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)$/i);
    if (!match) return 10 * 1024 * 1024; // Default 10MB
    return parseFloat(match[1]) * units[match[2].toUpperCase()];
  }

  /**
   * Get all files from base directory recursively
   * @returns {Promise<Array>} Array of file objects
   */
  async getAllFiles() {
    try {
      const files = [];
      await this.scanDirectory(this.baseDirectory, files);
      
      logger.info(`Found ${files.length} files in base directory`);
      return files;
    } catch (error) {
      logger.error('Failed to get all files:', error);
      throw error;
    }
  }

  /**
   * Recursively scan directory for files
   * @param {string} dirPath - Directory path to scan
   * @param {Array} files - Array to store file objects
   */
  async scanDirectory(dirPath, files) {
    const items = await fs.readdir(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stats = await fs.stat(itemPath);
      
      if (stats.isDirectory()) {
        await this.scanDirectory(itemPath, files);
      } else if (stats.isFile()) {
        const ext = path.extname(item).toLowerCase().slice(1);
        if (this.allowedFormats.includes(ext)) {
          files.push({
            id: uuidv4(),
            name: item,
            path: itemPath,
            relativePath: path.relative(this.baseDirectory, itemPath),
            size: stats.size,
            type: ext,
            lastModified: stats.mtime,
            created: stats.birthtime
          });
        }
      }
    }
  }

  /**
   * Extract text content from a file
   * @param {string} filePath - Path to the file
   * @returns {Promise<string>} Extracted text content
   */
  async extractTextFromFile(filePath) {
    try {
      const ext = path.extname(filePath).toLowerCase().slice(1);
      let content = '';

      switch (ext) {
        case 'pdf':
          content = await this.extractFromPDF(filePath);
          break;
        case 'doc':
        case 'docx':
          content = await this.extractFromWord(filePath);
          break;
        case 'txt':
          content = await this.extractFromText(filePath);
          break;
        case 'md':
          content = await this.extractFromMarkdown(filePath);
          break;
        case 'html':
          content = await this.extractFromHTML(filePath);
          break;
        default:
          throw new Error(`Unsupported file format: ${ext}`);
      }

      logger.info(`Extracted ${content.length} characters from ${path.basename(filePath)}`);
      return content;
    } catch (error) {
      logger.error(`Failed to extract text from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Extract text from PDF file
   * @param {string} filePath - Path to PDF file
   * @returns {Promise<string>} Extracted text
   */
  async extractFromPDF(filePath) {
    const dataBuffer = await fs.readFile(filePath);
    const data = await pdfParse(dataBuffer);
    return data.text;
  }

  /**
   * Extract text from Word document
   * @param {string} filePath - Path to Word file
   * @returns {Promise<string>} Extracted text
   */
  async extractFromWord(filePath) {
    const result = await mammoth.extractRawText({ path: filePath });
    return result.value;
  }

  /**
   * Extract text from plain text file
   * @param {string} filePath - Path to text file
   * @returns {Promise<string>} File content
   */
  async extractFromText(filePath) {
    return await fs.readFile(filePath, 'utf8');
  }

  /**
   * Extract text from Markdown file
   * @param {string} filePath - Path to Markdown file
   * @returns {Promise<string>} Extracted text
   */
  async extractFromMarkdown(filePath) {
    const markdown = await fs.readFile(filePath, 'utf8');
    const html = marked(markdown);
    const $ = cheerio.load(html);
    return $.text();
  }

  /**
   * Extract text from HTML file
   * @param {string} filePath - Path to HTML file
   * @returns {Promise<string>} Extracted text
   */
  async extractFromHTML(filePath) {
    const html = await fs.readFile(filePath, 'utf8');
    const $ = cheerio.load(html);
    return $.text();
  }

  /**
   * Process all files and extract content for RAG
   * @returns {Promise<Array>} Array of processed documents
   */
  async processAllFilesForRAG() {
    try {
      const files = await this.getAllFiles();
      const documents = [];

      for (const file of files) {
        try {
          const content = await this.extractTextFromFile(file.path);
          const chunks = this.chunkText(content, config.rag.chunkSize, config.rag.overlap);
          
          chunks.forEach((chunk, index) => {
            documents.push({
              id: `${file.id}_chunk_${index}`,
              title: `${file.name} - Part ${index + 1}`,
              content: chunk,
              metadata: {
                sourceFile: file.name,
                filePath: file.relativePath,
                fileType: file.type,
                chunkIndex: index,
                totalChunks: chunks.length,
                lastModified: file.lastModified
              }
            });
          });
        } catch (error) {
          logger.error(`Failed to process file ${file.name}:`, error);
          // Continue with other files
        }
      }

      logger.info(`Processed ${files.length} files into ${documents.length} document chunks`);
      return documents;
    } catch (error) {
      logger.error('Failed to process files for RAG:', error);
      throw error;
    }
  }

  /**
   * Split text into chunks with overlap
   * @param {string} text - Text to chunk
   * @param {number} chunkSize - Size of each chunk
   * @param {number} overlap - Overlap between chunks
   * @returns {Array<string>} Array of text chunks
   */
  chunkText(text, chunkSize = 1000, overlap = 200) {
    const chunks = [];
    let start = 0;

    while (start < text.length) {
      let end = start + chunkSize;
      
      // Try to break at sentence boundaries
      if (end < text.length) {
        const lastPeriod = text.lastIndexOf('.', end);
        const lastNewline = text.lastIndexOf('\n', end);
        const breakPoint = Math.max(lastPeriod, lastNewline);
        
        if (breakPoint > start + chunkSize * 0.5) {
          end = breakPoint + 1;
        }
      }

      chunks.push(text.slice(start, end).trim());
      start = end - overlap;
    }

    return chunks.filter(chunk => chunk.length > 0);
  }

  /**
   * Save uploaded file
   * @param {Object} file - Multer file object
   * @returns {Promise<Object>} Saved file information
   */
  async saveUploadedFile(file) {
    try {
      const ext = path.extname(file.originalname).toLowerCase().slice(1);
      
      if (!this.allowedFormats.includes(ext)) {
        throw new Error(`File format ${ext} is not allowed`);
      }

      if (file.size > this.maxFileSize) {
        throw new Error(`File size exceeds maximum allowed size of ${config.files.maxFileSize}`);
      }

      const fileName = `${uuidv4()}_${file.originalname}`;
      const filePath = path.join(this.uploadDirectory, fileName);
      
      await fs.writeFile(filePath, file.buffer);
      
      const fileInfo = {
        id: uuidv4(),
        name: file.originalname,
        fileName: fileName,
        path: filePath,
        size: file.size,
        type: ext,
        uploadDate: new Date().toISOString(),
        mimetype: file.mimetype
      };

      logger.info(`File uploaded successfully: ${file.originalname}`);
      return fileInfo;
    } catch (error) {
      logger.error('Failed to save uploaded file:', error);
      throw error;
    }
  }

  /**
   * Delete a file
   * @param {string} filePath - Path to file to delete
   * @returns {Promise<boolean>} Success status
   */
  async deleteFile(filePath) {
    try {
      await fs.remove(filePath);
      logger.info(`File deleted successfully: ${filePath}`);
      return true;
    } catch (error) {
      logger.error(`Failed to delete file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Start watching base directory for changes
   * @param {Function} callback - Callback function for file changes
   */
  startWatching(callback) {
    if (this.watcher) {
      this.watcher.close();
    }

    this.watcher = chokidar.watch(this.baseDirectory, {
      ignored: /(^|[\/\\])\../, // ignore dotfiles
      persistent: true
    });

    this.watcher
      .on('add', path => {
        logger.info(`File added: ${path}`);
        callback('add', path);
      })
      .on('change', path => {
        logger.info(`File changed: ${path}`);
        callback('change', path);
      })
      .on('unlink', path => {
        logger.info(`File removed: ${path}`);
        callback('unlink', path);
      })
      .on('error', error => {
        logger.error('File watcher error:', error);
      });

    logger.info('File watcher started');
  }

  /**
   * Stop watching directory
   */
  stopWatching() {
    if (this.watcher) {
      this.watcher.close();
      this.watcher = null;
      logger.info('File watcher stopped');
    }
  }
}

module.exports = new FileService();