const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const straicoService = require('./straicoService');

class ChatService {
  constructor() {
    this.conversations = new Map(); // In-memory storage for conversations
    this.defaultSystemPrompt = `Sei un assistente AI specializzato nel fornire informazioni sui documenti tecnici di Softway. 
    Utilizza le informazioni fornite nel contesto per rispondere alle domande degli utenti in modo accurato e dettagliato.
    Se non trovi informazioni specifiche nei documenti, indica chiaramente che l'informazione non è disponibile nei documenti forniti.
    Rispondi sempre in italiano e mantieni un tono professionale e cortese.`;
  }

  /**
   * Process a chat message
   * @param {Object} messageData - Message data
   * @returns {Promise<Object>} Chat response
   */
  async processMessage(messageData) {
    try {
      const {
        message,
        conversationId = uuidv4(),
        userId = 'anonymous',
        ragId,
        systemPrompt = this.defaultSystemPrompt
      } = messageData;

      // Get or create conversation
      let conversation = this.getConversation(conversationId);
      if (!conversation) {
        conversation = this.createConversation(conversationId, userId);
      }

      // Add user message to conversation
      const userMessage = {
        id: uuidv4(),
        role: 'user',
        content: message,
        timestamp: new Date().toISOString()
      };
      conversation.messages.push(userMessage);

      // Send message to Straico
      const chatResponse = await straicoService.sendChatMessage({
        message,
        ragId,
        conversationId,
        systemPrompt,
        conversationHistory: this.getRecentMessages(conversation, 10)
      });

      // Add assistant response to conversation
      const assistantMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: chatResponse.response,
        timestamp: chatResponse.timestamp
      };
      conversation.messages.push(assistantMessage);

      // Update conversation metadata
      conversation.lastActivity = new Date().toISOString();
      conversation.messageCount = conversation.messages.length;

      logger.info(`Chat message processed for conversation ${conversationId}`);

      return {
        messageId: assistantMessage.id,
        response: chatResponse.response,
        conversationId,
        timestamp: chatResponse.timestamp
      };
    } catch (error) {
      logger.error('Failed to process chat message:', error);
      throw new Error(`Chat processing failed: ${error.message}`);
    }
  }

  /**
   * Get conversation by ID
   * @param {string} conversationId - Conversation ID
   * @returns {Object|null} Conversation object
   */
  getConversation(conversationId) {
    return this.conversations.get(conversationId) || null;
  }

  /**
   * Create a new conversation
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @returns {Object} New conversation object
   */
  createConversation(conversationId, userId) {
    const conversation = {
      id: conversationId,
      userId,
      messages: [],
      created: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      messageCount: 0,
      metadata: {}
    };

    this.conversations.set(conversationId, conversation);
    logger.info(`New conversation created: ${conversationId}`);
    return conversation;
  }

  /**
   * Get conversation history
   * @param {string} conversationId - Conversation ID
   * @param {number} limit - Maximum number of messages to return
   * @returns {Object} Conversation history
   */
  getConversationHistory(conversationId, limit = 50) {
    const conversation = this.getConversation(conversationId);
    if (!conversation) {
      throw new Error('Conversation not found');
    }

    const messages = conversation.messages.slice(-limit);
    return {
      conversationId,
      messages,
      totalMessages: conversation.messageCount,
      created: conversation.created,
      lastActivity: conversation.lastActivity
    };
  }

  /**
   * Get recent messages for context
   * @param {Object} conversation - Conversation object
   * @param {number} count - Number of recent messages
   * @returns {Array} Recent messages
   */
  getRecentMessages(conversation, count = 10) {
    return conversation.messages.slice(-count);
  }



  /**
   * Delete conversation
   * @param {string} conversationId - Conversation ID
   * @returns {boolean} Success status
   */
  deleteConversation(conversationId) {
    const deleted = this.conversations.delete(conversationId);
    if (deleted) {
      logger.info(`Conversation deleted: ${conversationId}`);
    }
    return deleted;
  }

  /**
   * Get all conversations for a user
   * @param {string} userId - User ID
   * @param {number} limit - Maximum number of conversations
   * @returns {Array} User conversations
   */
  getUserConversations(userId, limit = 20) {
    const userConversations = [];
    
    for (const conversation of this.conversations.values()) {
      if (conversation.userId === userId) {
        userConversations.push({
          id: conversation.id,
          created: conversation.created,
          lastActivity: conversation.lastActivity,
          messageCount: conversation.messageCount,
          lastMessage: conversation.messages[conversation.messages.length - 1]?.content?.substring(0, 100) || ''
        });
      }
    }

    // Sort by last activity (most recent first)
    userConversations.sort((a, b) => new Date(b.lastActivity) - new Date(a.lastActivity));
    
    return userConversations.slice(0, limit);
  }

  /**
   * Search conversations by content
   * @param {string} query - Search query
   * @param {string} userId - User ID (optional)
   * @returns {Array} Search results
   */
  searchConversations(query, userId = null) {
    const results = [];
    const searchTerm = query.toLowerCase();

    for (const conversation of this.conversations.values()) {
      if (userId && conversation.userId !== userId) {
        continue;
      }

      for (const message of conversation.messages) {
        if (message.content.toLowerCase().includes(searchTerm)) {
          results.push({
            conversationId: conversation.id,
            messageId: message.id,
            content: message.content,
            role: message.role,
            timestamp: message.timestamp,
            context: this.getMessageContext(conversation, message.id)
          });
        }
      }
    }

    return results;
  }

  /**
   * Get context around a specific message
   * @param {Object} conversation - Conversation object
   * @param {string} messageId - Message ID
   * @returns {Array} Context messages
   */
  getMessageContext(conversation, messageId, contextSize = 2) {
    const messageIndex = conversation.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return [];

    const start = Math.max(0, messageIndex - contextSize);
    const end = Math.min(conversation.messages.length, messageIndex + contextSize + 1);
    
    return conversation.messages.slice(start, end);
  }

  /**
   * Get conversation statistics
   * @returns {Object} Statistics
   */
  getStatistics() {
    const totalConversations = this.conversations.size;
    let totalMessages = 0;
    let activeConversations = 0;
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    for (const conversation of this.conversations.values()) {
      totalMessages += conversation.messageCount;
      if (new Date(conversation.lastActivity) > oneDayAgo) {
        activeConversations++;
      }
    }

    return {
      totalConversations,
      totalMessages,
      activeConversations,
      averageMessagesPerConversation: totalConversations > 0 ? totalMessages / totalConversations : 0
    };
  }

  /**
   * Clean up old conversations
   * @param {number} maxAge - Maximum age in days
   * @returns {number} Number of conversations cleaned up
   */
  cleanupOldConversations(maxAge = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - maxAge);
    
    let cleanedCount = 0;
    
    for (const [id, conversation] of this.conversations.entries()) {
      if (new Date(conversation.lastActivity) < cutoffDate) {
        this.conversations.delete(id);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.info(`Cleaned up ${cleanedCount} old conversations`);
    }

    return cleanedCount;
  }
}

module.exports = new ChatService();