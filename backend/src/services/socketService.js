const logger = require('../utils/logger');
const chatService = require('./chatService');

class SocketService {
  constructor() {
    this.connectedUsers = new Map();
  }

  /**
   * Setup Socket.IO event handlers
   * @param {Object} io - Socket.IO server instance
   */
  setupSocketHandlers(io) {
    io.on('connection', (socket) => {
      logger.info(`User connected: ${socket.id}`);
      
      // Handle user authentication/identification
      socket.on('user:identify', (userData) => {
        this.handleUserIdentify(socket, userData);
      });

      // Handle chat messages
      socket.on('chat:message', async (messageData) => {
        await this.handleChatMessage(socket, messageData);
      });

      // Handle typing indicators
      socket.on('chat:typing', (data) => {
        this.handleTyping(socket, data);
      });

      socket.on('chat:stop_typing', (data) => {
        this.handleStopTyping(socket, data);
      });

      // Handle conversation management
      socket.on('conversation:join', (conversationId) => {
        this.handleJoinConversation(socket, conversationId);
      });

      socket.on('conversation:leave', (conversationId) => {
        this.handleLeaveConversation(socket, conversationId);
      });

      socket.on('conversation:history', (data) => {
        this.handleGetHistory(socket, data);
      });

      // Handle file operations
      socket.on('file:upload_progress', (data) => {
        this.handleFileUploadProgress(socket, data);
      });

      // Handle RAG operations
      socket.on('rag:status', (ragId) => {
        this.handleRAGStatus(socket, ragId);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        this.handleDisconnect(socket);
      });

      // Error handling
      socket.on('error', (error) => {
        logger.error(`Socket error for ${socket.id}:`, error);
        socket.emit('error', { message: 'An error occurred' });
      });
    });

    logger.info('Socket.IO handlers setup complete');
  }

  /**
   * Handle user identification
   * @param {Object} socket - Socket instance
   * @param {Object} userData - User data
   */
  handleUserIdentify(socket, userData) {
    try {
      const user = {
        id: userData.userId || socket.id,
        name: userData.name || 'Anonymous',
        socketId: socket.id,
        connectedAt: new Date().toISOString()
      };

      this.connectedUsers.set(socket.id, user);
      socket.userId = user.id;
      
      socket.emit('user:identified', { user });
      logger.info(`User identified: ${user.id} (${user.name})`);
    } catch (error) {
      logger.error('Error in user identification:', error);
      socket.emit('error', { message: 'Failed to identify user' });
    }
  }

  /**
   * Handle chat message
   * @param {Object} socket - Socket instance
   * @param {Object} messageData - Message data
   */
  async handleChatMessage(socket, messageData) {
    try {
      // Emit typing stopped for this user
      socket.to(messageData.conversationId).emit('chat:user_stopped_typing', {
        userId: socket.userId,
        conversationId: messageData.conversationId
      });

      // Emit bot typing indicator
      socket.emit('chat:bot_typing', {
        conversationId: messageData.conversationId
      });

      // Process the message
      const response = await chatService.processMessage({
        ...messageData,
        userId: socket.userId
      });

      // Stop bot typing indicator
      socket.emit('chat:bot_stopped_typing', {
        conversationId: messageData.conversationId
      });

      // Emit the response
      socket.emit('chat:response', response);

      // Emit to other users in the conversation (if any)
      socket.to(messageData.conversationId).emit('chat:new_message', {
        conversationId: response.conversationId,
        message: {
          id: response.messageId,
          role: 'assistant',
          content: response.response,
          timestamp: response.timestamp
        }
      });

      logger.info(`Chat message processed for user ${socket.userId}`);
    } catch (error) {
      logger.error('Error processing chat message:', error);
      
      // Stop bot typing indicator
      socket.emit('chat:bot_stopped_typing', {
        conversationId: messageData.conversationId
      });

      socket.emit('chat:error', {
        conversationId: messageData.conversationId,
        error: 'Failed to process message',
        details: error.message
      });
    }
  }

  /**
   * Handle typing indicator
   * @param {Object} socket - Socket instance
   * @param {Object} data - Typing data
   */
  handleTyping(socket, data) {
    socket.to(data.conversationId).emit('chat:user_typing', {
      userId: socket.userId,
      userName: this.connectedUsers.get(socket.id)?.name || 'Anonymous',
      conversationId: data.conversationId
    });
  }

  /**
   * Handle stop typing indicator
   * @param {Object} socket - Socket instance
   * @param {Object} data - Stop typing data
   */
  handleStopTyping(socket, data) {
    socket.to(data.conversationId).emit('chat:user_stopped_typing', {
      userId: socket.userId,
      conversationId: data.conversationId
    });
  }

  /**
   * Handle joining a conversation
   * @param {Object} socket - Socket instance
   * @param {string} conversationId - Conversation ID
   */
  handleJoinConversation(socket, conversationId) {
    try {
      socket.join(conversationId);
      socket.emit('conversation:joined', { conversationId });
      
      // Notify other users in the conversation
      socket.to(conversationId).emit('conversation:user_joined', {
        userId: socket.userId,
        userName: this.connectedUsers.get(socket.id)?.name || 'Anonymous',
        conversationId
      });

      logger.info(`User ${socket.userId} joined conversation ${conversationId}`);
    } catch (error) {
      logger.error('Error joining conversation:', error);
      socket.emit('error', { message: 'Failed to join conversation' });
    }
  }

  /**
   * Handle leaving a conversation
   * @param {Object} socket - Socket instance
   * @param {string} conversationId - Conversation ID
   */
  handleLeaveConversation(socket, conversationId) {
    try {
      socket.leave(conversationId);
      socket.emit('conversation:left', { conversationId });
      
      // Notify other users in the conversation
      socket.to(conversationId).emit('conversation:user_left', {
        userId: socket.userId,
        conversationId
      });

      logger.info(`User ${socket.userId} left conversation ${conversationId}`);
    } catch (error) {
      logger.error('Error leaving conversation:', error);
      socket.emit('error', { message: 'Failed to leave conversation' });
    }
  }

  /**
   * Handle getting conversation history
   * @param {Object} socket - Socket instance
   * @param {Object} data - History request data
   */
  handleGetHistory(socket, data) {
    try {
      const history = chatService.getConversationHistory(
        data.conversationId,
        data.limit || 50
      );
      
      socket.emit('conversation:history', history);
    } catch (error) {
      logger.error('Error getting conversation history:', error);
      socket.emit('error', { message: 'Failed to get conversation history' });
    }
  }

  /**
   * Handle file upload progress
   * @param {Object} socket - Socket instance
   * @param {Object} data - Upload progress data
   */
  handleFileUploadProgress(socket, data) {
    socket.emit('file:upload_progress', data);
  }

  /**
   * Handle RAG status request
   * @param {Object} socket - Socket instance
   * @param {string} ragId - RAG ID
   */
  async handleRAGStatus(socket, ragId) {
    try {
      // This would typically call the RAG service to get status
      // For now, we'll emit a placeholder response
      socket.emit('rag:status_update', {
        ragId,
        status: 'active',
        documentsCount: 0,
        lastUpdated: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Error getting RAG status:', error);
      socket.emit('error', { message: 'Failed to get RAG status' });
    }
  }

  /**
   * Handle user disconnection
   * @param {Object} socket - Socket instance
   */
  handleDisconnect(socket) {
    const user = this.connectedUsers.get(socket.id);
    if (user) {
      this.connectedUsers.delete(socket.id);
      logger.info(`User disconnected: ${user.id} (${user.name})`);
    } else {
      logger.info(`Unknown user disconnected: ${socket.id}`);
    }
  }

  /**
   * Broadcast message to all connected users
   * @param {string} event - Event name
   * @param {Object} data - Data to broadcast
   */
  broadcast(io, event, data) {
    io.emit(event, data);
    logger.info(`Broadcasted ${event} to all connected users`);
  }

  /**
   * Send message to specific user
   * @param {Object} io - Socket.IO server instance
   * @param {string} userId - User ID
   * @param {string} event - Event name
   * @param {Object} data - Data to send
   */
  sendToUser(io, userId, event, data) {
    const user = Array.from(this.connectedUsers.values()).find(u => u.id === userId);
    if (user) {
      io.to(user.socketId).emit(event, data);
      logger.info(`Sent ${event} to user ${userId}`);
    }
  }

  /**
   * Get connected users count
   * @returns {number} Number of connected users
   */
  getConnectedUsersCount() {
    return this.connectedUsers.size;
  }

  /**
   * Get connected users list
   * @returns {Array} List of connected users
   */
  getConnectedUsers() {
    return Array.from(this.connectedUsers.values());
  }
}

const socketService = new SocketService();

module.exports = {
  setupSocketHandlers: (io) => socketService.setupSocketHandlers(io),
  broadcast: (io, event, data) => socketService.broadcast(io, event, data),
  sendToUser: (io, userId, event, data) => socketService.sendToUser(io, userId, event, data),
  getConnectedUsersCount: () => socketService.getConnectedUsersCount(),
  getConnectedUsers: () => socketService.getConnectedUsers()
};