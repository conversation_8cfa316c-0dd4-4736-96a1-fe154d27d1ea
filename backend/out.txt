{"data": [{"name": "Amazon: Nova Lite 1.0", "model": "amazon/nova-lite-v1", "pricing": {"coins": 0.2, "words": 100}, "max_output": 5000}, {"name": "Amazon: Nova Micro 1.0", "model": "amazon/nova-micro-v1", "pricing": {"coins": 0.1, "words": 100}, "max_output": 5000}, {"name": "Amazon: Nova Pro 1.0", "model": "amazon/nova-pro-v1", "pricing": {"coins": 1, "words": 100}, "max_output": 5000}, {"name": "Anthropic: <PERSON> 3 Opus", "model": "anthropic/claude-3-opus", "pricing": {"coins": 24, "words": 100}, "max_output": 4096}, {"name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON>", "model": "anthropic/claude-3-5-haiku-20241022", "pricing": {"coins": 1.6, "words": 100}, "max_output": 8192}, {"name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON>", "model": "anthropic/claude-3.5-sonnet", "pricing": {"coins": 4.8, "words": 100}, "max_output": 8192}, {"name": "Anthropic: <PERSON> 3.7 Sonnet Reasoning (High)", "model": "anthropic/claude-3.7-sonnet:thinking", "pricing": {"coins": 6, "words": 100}, "max_output": 8192}, {"name": "Anthropic: <PERSON> 3.7 Sonnet Reasoning (Medium)", "model": "anthropic/claude-3.7-sonnet", "pricing": {"coins": 5, "words": 100}, "max_output": 8192}, {"name": "Anthropic: <PERSON> 4", "model": "anthropic/claude-sonnet-4", "pricing": {"coins": 6, "words": 100}, "max_output": 64000}, {"name": "Cohere: Command R (08-2024)", "model": "cohere/command-r-08-2024", "pricing": {"coins": 0.2, "words": 100}, "max_output": 4000}, {"name": "Cohere: Command R+ (08-2024)", "model": "cohere/command-r-plus-08-2024", "pricing": {"coins": 3.4, "words": 100}, "max_output": 4000}, {"name": "DeepSeek V3", "model": "deepseek/deepseek-chat", "pricing": {"coins": 1.1, "words": 100}, "max_output": 8000}, {"name": "DeepSeek: DeepSeek R1 Reasoning", "model": "deepseek/deepseek-r1", "pricing": {"coins": 5.16, "words": 100}, "max_output": 8000}, {"name": "DeepSeek: DeepSeek R1 Reasoning (nitro)", "model": "deepseek/deepseek-r1:nitro", "pricing": {"coins": 5.16, "words": 100}, "max_output": 8000}, {"name": "DeepSeek: DeepSeek V3 0324", "model": "deepseek/deepseek-chat-v3-0324", "pricing": {"coins": 1, "words": 100}, "max_output": 8000}, {"name": "Dolphin 2.6 Mixtral 8x7B", "model": "cognitivecomputations/dolphin-mixtral-8x7b", "pricing": {"coins": 1, "words": 100}, "max_output": 32768}, {"name": "Goliath 120B", "model": "alpindale/goliath-120b", "pricing": {"coins": 5, "words": 100}, "max_output": 400}, {"name": "Google: Gemini 2.5 Flash Preview", "model": "google/gemini-2.5-flash-preview", "pricing": {"coins": 0.5, "words": 100}, "max_output": 66000}, {"name": "Google: Gemini 2.5 Flash Preview Reasoning", "model": "google/gemini-2.5-flash-preview:thinking", "pricing": {"coins": 1.2, "words": 100}, "max_output": 66000}, {"name": "Google: Gemini Flash 2.08B", "model": "google/gemini-2.0-flash-001", "pricing": {"coins": 0.2, "words": 100}, "max_output": 100000}, {"name": "Google: Gemini Pro 1.5", "model": "google/gemini-pro-1.5", "pricing": {"coins": 3.7, "words": 100}, "max_output": 8192}, {"name": "Google: Gemini Pro 2.5 Reasoning (Preview)", "model": "google/gemini-2.5-pro-preview", "pricing": {"coins": 16.6, "words": 100}, "max_output": 100000}, {"name": "Google: Gemma 2 27B", "model": "google/gemma-2-27b-it", "pricing": {"coins": 0.4, "words": 100}, "max_output": 8192}, {"name": "Gryphe: MythoMax L2 13B 8k", "model": "gryphe/mythomax-l2-13b", "pricing": {"coins": 1, "words": 100}, "max_output": 4096}, {"name": "Meta: Llama 3 70B Instruct (nitro)", "model": "meta-llama/llama-3-70b-instruct:nitro", "pricing": {"coins": 1, "words": 100}, "max_output": 8192}, {"name": "Meta: Llama 3.1 405B Instruct", "model": "meta-llama/llama-3.1-405b-instruct", "pricing": {"coins": 1.6, "words": 100}, "max_output": 32768}, {"name": "Meta: Llama 3.1 70B Instruct", "model": "meta-llama/llama-3.1-70b-instruct", "pricing": {"coins": 0.7, "words": 100}, "max_output": 32768}, {"name": "Meta: Llama 3.3 70B Instruct", "model": "meta-llama/llama-3.3-70b-instruct", "pricing": {"coins": 0.5, "words": 100}, "max_output": 131000}, {"name": "Meta: Llama 4 Maverick", "model": "meta-llama/llama-4-maverick", "pricing": {"coins": 0.7, "words": 100}, "max_output": 131000}, {"name": "Microsoft: Phi 4", "model": "microsoft/phi-4", "pricing": {"coins": 0.1, "words": 100}, "max_output": 16000}, {"name": "Mistral: Codestral Mamba", "model": "mistralai/codestral-mamba", "pricing": {"coins": 0.2, "words": 100}, "max_output": 256000}, {"name": "Mistral: Mistral Medium 3", "model": "mistralai/mistral-medium-3", "pricing": {"coins": 1, "words": 100}, "max_output": 32768}, {"name": "Mistral: Mixtral 8x7B", "model": "mistralai/mixtral-8x7b-instruct", "pricing": {"coins": 1, "words": 100}, "max_output": 32768}, {"name": "NVIDIA: Llama 3.1 Nemotron 70B Instruct", "model": "nvidia/llama-3.1-nemotron-70b-instruct", "pricing": {"coins": 0.5, "words": 100}, "max_output": 131072}, {"name": "NVIDIA: Llama 3.1 Nemotron Ultra 253B v1", "model": "nvidia/llama-3.1-nemotron-ultra-253b-v1:free", "pricing": {"coins": 0.5, "words": 100}, "max_output": 131072}, {"name": "NVIDIA: Llama 3.3 Nemotron Super 49B v1", "model": "nvidia/llama-3.1-nemotron-70b-instruct", "pricing": {"coins": 0.5, "words": 100}, "max_output": 131072}, {"name": "OpenAI: GPT-4.1", "model": "openai/gpt-4.1", "pricing": {"coins": 3, "words": 100}, "max_output": 1047576}, {"name": "OpenAI: GPT-4.1 Mini", "model": "openai/gpt-4.1-mini", "pricing": {"coins": 1, "words": 100}, "max_output": 1047576}, {"name": "OpenAI: GPT-4.1 Nano", "model": "openai/gpt-4.1-nano", "pricing": {"coins": 0.5, "words": 100}, "max_output": 1047576}, {"name": "OpenAI: GPT-4o - (Aug-06)", "model": "openai/gpt-4o-2024-08-06", "pricing": {"coins": 3, "words": 100}, "max_output": 16384}, {"name": "OpenAI: GPT-4o - (Nov-20)", "model": "openai/gpt-4o-2024-11-20", "pricing": {"coins": 3.3, "words": 100}, "max_output": 16000}, {"name": "OpenAI: GPT-4o mini", "model": "openai/gpt-4o-mini", "pricing": {"coins": 0.4, "words": 100}, "max_output": 16384}, {"name": "OpenAI: o1", "model": "openai/o1", "pricing": {"coins": 20, "words": 100}, "max_output": 100000}, {"name": "OpenAI: o1 High Reasoning", "model": "openai/o1-pro", "pricing": {"coins": 35, "words": 100}, "max_output": 100000}, {"name": "OpenAI: o1 mini", "model": "openai/o1-mini", "pricing": {"coins": 4, "words": 100}, "max_output": 65536}, {"name": "OpenAI: o3", "model": "o3-2025-04-16", "pricing": {"coins": 20, "words": 100}, "max_output": 100000}, {"name": "OpenAI: o3 Mini (High)", "model": "openai/o3-mini-high", "pricing": {"coins": 3, "words": 100}, "max_output": 100000}, {"name": "OpenAI: o3 Mini (Medium)", "model": "openai/o3-mini", "pricing": {"coins": 1.5, "words": 100}, "max_output": 100000}, {"name": "OpenAI: o4 Mini", "model": "openai/o4-mini", "pricing": {"coins": 1.5, "words": 100}, "max_output": 100000}, {"name": "OpenAI: o4 Mini High", "model": "openai/o4-mini-high", "pricing": {"coins": 2.4, "words": 100}, "max_output": 100000}, {"name": "Perplexity: Llama 3.1 Sonar 70B Online", "model": "perplexity/llama-3.1-sonar-large-128k-online", "pricing": {"coins": 0.6, "words": 100}, "max_output": 127072}, {"name": "Perplexity: Llama 3.1 Sonar 8B Online", "model": "perplexity/llama-3.1-sonar-small-128k-online", "pricing": {"coins": 0.2, "words": 100}, "max_output": 127072}, {"name": "Perplexity: Sonar", "model": "perplexity/sonar", "pricing": {"coins": 1, "words": 100}, "max_output": 127072}, {"name": "Perplexity: Sonar Deep Research Reasoning", "model": "perplexity/sonar-deep-research", "pricing": {"coins": 192, "words": 100}, "max_output": 127072}, {"name": "Perplexity: <PERSON><PERSON> Reasoning", "model": "perplexity/sonar-reasoning", "pricing": {"coins": 2.2, "words": 100}, "max_output": 127072}, {"name": "Qwen 2 72B Instruct", "model": "qwen/qwen-2-72b-instruct", "pricing": {"coins": 0.5, "words": 100}, "max_output": 32768}, {"name": "Qwen2-VL 72B Instruct", "model": "qwen/qwen-2-vl-72b-instruct", "pricing": {"coins": 0.2, "words": 100}, "max_output": 4096}, {"name": "Qwen2.5 72B Instruct", "model": "qwen/qwen-2.5-72b-instruct", "pricing": {"coins": 0.2, "words": 100}, "max_output": 32768}, {"name": "Qwen2.5 Coder 32B Instruct", "model": "qwen/qwen-2.5-coder-32b-instruct", "pricing": {"coins": 0.5, "words": 100}, "max_output": 4000}, {"name": "Qwen: Qwen2.5 VL 32B Instruct", "model": "qwen/qwen2.5-vl-32b-instruct:free", "pricing": {"coins": 0.7, "words": 100}, "max_output": 33000}, {"name": "Qwen: Qwen3 235B A22B Reasoning", "model": "qwen/qwen3-235b-a22b", "pricing": {"coins": 0.6, "words": 100}, "max_output": 33000}, {"name": "WizardLM-2 8x22B", "model": "microsoft/wizardlm-2-8x22b", "pricing": {"coins": 0.6, "words": 100}, "max_output": 66000}, {"name": "xAI: Grok 2 1212", "model": "x-ai/grok-2-1212", "pricing": {"coins": 3.2, "words": 100}, "max_output": 131000}, {"name": "xAI: Grok 3 Beta", "model": "x-ai/grok-3-beta", "pricing": {"coins": 8, "words": 100}, "max_output": 131000}, {"name": "xAI: Grok 3 Mini Beta Reasoning", "model": "x-ai/grok-3-mini-beta", "pricing": {"coins": 1.3, "words": 100}, "max_output": 131000}], "success": true}