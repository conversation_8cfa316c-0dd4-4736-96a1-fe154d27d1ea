#!/usr/bin/env node

/**
 * Script per creare una RAG (Retrieval-Augmented Generation) utilizzando Straico
 * Legge tutti i file dalla directory base e li carica nella RAG
 */

const path = require('path');
const fs = require('fs-extra');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const fileService = require('../src/services/fileService');
const straicoService = require('../src/services/straicoService');
const logger = require('../src/utils/logger');

class RAGCreator {
  constructor() {
    this.ragName = process.env.RAG_NAME || 'Softway-Documents-RAG';
    this.ragDescription = process.env.RAG_DESCRIPTION || 'Knowledge base created from Softway technical documents';
    this.verbose = process.argv.includes('--verbose') || process.argv.includes('-v');
    this.force = process.argv.includes('--force') || process.argv.includes('-f');
    this.dryRun = process.argv.includes('--dry-run') || process.argv.includes('-d');
  }

  /**
   * Log message with timestamp
   * @param {string} message - Message to log
   * @param {string} level - Log level
   */
  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [RAG Creator]`;
    
    if (level === 'error') {
      console.error(`${prefix} ERROR: ${message}`);
      logger.error(message);
    } else if (level === 'warn') {
      console.warn(`${prefix} WARN: ${message}`);
      logger.warn(message);
    } else if (level === 'verbose' && this.verbose) {
      console.log(`${prefix} VERBOSE: ${message}`);
      logger.info(message);
    } else {
      console.log(`${prefix} ${message}`);
      logger.info(message);
    }
  }

  /**
   * Display progress bar
   * @param {number} current - Current progress
   * @param {number} total - Total items
   * @param {string} label - Progress label
   */
  showProgress(current, total, label = 'Progress') {
    const percentage = Math.round((current / total) * 100);
    const barLength = 30;
    const filledLength = Math.round((barLength * current) / total);
    const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
    
    process.stdout.write(`\r${label}: [${bar}] ${percentage}% (${current}/${total})`);
    
    if (current === total) {
      process.stdout.write('\n');
    }
  }

  /**
   * Validate environment and dependencies
   */
  async validateEnvironment() {
    this.log('Validating environment...');

    // Check if Straico API key is configured
    if (!process.env.STRAICO_API_KEY || process.env.STRAICO_API_KEY === 'STRAICO_API_KEY') {
      throw new Error('STRAICO_API_KEY environment variable is not configured');
    }

    // Test Straico connection
    this.log('Testing Straico API connection...');
    const isConnected = await straicoService.testConnection();
    if (!isConnected) {
      throw new Error('Cannot connect to Straico API. Please check your API key and network connection.');
    }

    this.log('Environment validation completed successfully', 'verbose');
  }

  /**
   * Scan and validate files
   */
  async scanFiles() {
    this.log('Scanning files in base directory...');
    
    const files = await fileService.getAllFiles();
    
    if (files.length === 0) {
      throw new Error('No supported files found in base directory');
    }

    this.log(`Found ${files.length} files to process`);
    
    if (this.verbose) {
      files.forEach(file => {
        this.log(`  - ${file.name} (${file.type}, ${this.formatFileSize(file.size)})`, 'verbose');
      });
    }

    return files;
  }

  /**
   * Process files and extract content
   * @param {Array} files - Files to process
   */
  async processFiles(files) {
    this.log('Processing files and extracting content...');
    
    const documents = [];
    const errors = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      try {
        this.showProgress(i + 1, files.length, 'Processing files');
        
        this.log(`Processing: ${file.name}`, 'verbose');
        
        const content = await fileService.extractTextFromFile(file.path);
        const chunks = fileService.chunkText(content);
        
        chunks.forEach((chunk, index) => {
          documents.push({
            id: `${file.id}_chunk_${index}`,
            title: `${file.name} - Part ${index + 1}`,
            content: chunk,
            metadata: {
              sourceFile: file.name,
              filePath: file.relativePath,
              fileType: file.type,
              chunkIndex: index,
              totalChunks: chunks.length,
              lastModified: file.lastModified,
              fileSize: file.size
            }
          });
        });

        this.log(`  ✓ Extracted ${chunks.length} chunks from ${file.name}`, 'verbose');
        
      } catch (error) {
        const errorMsg = `Failed to process ${file.name}: ${error.message}`;
        this.log(errorMsg, 'error');
        errors.push({ file: file.name, error: error.message });
      }
    }

    this.log(`\nProcessing completed:`);
    this.log(`  - Successfully processed: ${files.length - errors.length} files`);
    this.log(`  - Errors: ${errors.length} files`);
    this.log(`  - Total document chunks: ${documents.length}`);

    if (errors.length > 0 && this.verbose) {
      this.log('\nErrors encountered:', 'warn');
      errors.forEach(err => {
        this.log(`  - ${err.file}: ${err.error}`, 'warn');
      });
    }

    if (documents.length === 0) {
      throw new Error('No documents were successfully processed');
    }

    return { documents, errors };
  }

  /**
   * Create RAG with Straico
   * @param {Array} documents - Processed documents
   */
  async createRAG(documents) {
    if (this.dryRun) {
      this.log('DRY RUN: Would create RAG with the following data:');
      this.log(`  - Name: ${this.ragName}`);
      this.log(`  - Description: ${this.ragDescription}`);
      this.log(`  - Documents: ${documents.length}`);
      this.log(`  - Total content length: ${documents.reduce((sum, doc) => sum + doc.content.length, 0)} characters`);
      return { id: 'dry-run-rag-id', status: 'dry-run' };
    }

    this.log('Creating RAG knowledge base with Straico...');
    
    const ragData = {
      name: this.ragName,
      description: this.ragDescription,
      documents: documents
    };

    const ragResponse = await straicoService.createRAG(ragData);
    
    this.log(`✓ RAG created successfully with ID: ${ragResponse.id}`);
    
    return ragResponse;
  }

  /**
   * Save RAG information to file
   * @param {Object} ragInfo - RAG information
   * @param {Object} stats - Processing statistics
   */
  async saveRAGInfo(ragInfo, stats) {
    const ragInfoFile = path.join(__dirname, '../data/rag-info.json');
    
    const ragData = {
      ragId: ragInfo.id,
      name: this.ragName,
      description: this.ragDescription,
      status: ragInfo.status,
      createdAt: new Date().toISOString(),
      statistics: stats,
      lastUpdate: new Date().toISOString()
    };

    await fs.ensureDir(path.dirname(ragInfoFile));
    await fs.writeJson(ragInfoFile, ragData, { spaces: 2 });
    
    this.log(`RAG information saved to: ${ragInfoFile}`);
  }

  /**
   * Format file size for display
   * @param {number} bytes - File size in bytes
   * @returns {string} Formatted file size
   */
  formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * Display summary
   * @param {Object} ragInfo - RAG information
   * @param {Object} stats - Processing statistics
   */
  displaySummary(ragInfo, stats) {
    console.log('\n' + '='.repeat(60));
    console.log('RAG CREATION SUMMARY');
    console.log('='.repeat(60));
    console.log(`RAG ID: ${ragInfo.id}`);
    console.log(`Name: ${this.ragName}`);
    console.log(`Status: ${ragInfo.status || 'Created'}`);
    console.log(`Files processed: ${stats.filesProcessed}`);
    console.log(`Document chunks: ${stats.documentsCreated}`);
    console.log(`Total content: ${this.formatFileSize(stats.totalContentLength)}`);
    console.log(`Processing errors: ${stats.errors}`);
    console.log(`Created at: ${new Date().toISOString()}`);
    console.log('='.repeat(60));
    
    if (!this.dryRun) {
      console.log('\nRAG is ready to use! You can now start the chat application.');
      console.log(`Use RAG ID: ${ragInfo.id}`);
    }
  }

  /**
   * Main execution function
   */
  async run() {
    const startTime = Date.now();
    
    try {
      this.log('Starting RAG creation process...');
      
      // Validate environment
      await this.validateEnvironment();
      
      // Scan files
      const files = await this.scanFiles();
      
      // Process files
      const { documents, errors } = await this.processFiles(files);
      
      // Create RAG
      const ragInfo = await this.createRAG(documents);
      
      // Calculate statistics
      const stats = {
        filesProcessed: files.length - errors.length,
        documentsCreated: documents.length,
        totalContentLength: documents.reduce((sum, doc) => sum + doc.content.length, 0),
        errors: errors.length,
        processingTime: Date.now() - startTime
      };
      
      // Save RAG info
      if (!this.dryRun) {
        await this.saveRAGInfo(ragInfo, stats);
      }
      
      // Display summary
      this.displaySummary(ragInfo, stats);
      
      this.log(`RAG creation completed in ${(stats.processingTime / 1000).toFixed(2)} seconds`);
      
      process.exit(0);
      
    } catch (error) {
      this.log(`RAG creation failed: ${error.message}`, 'error');
      
      if (this.verbose) {
        console.error('\nFull error details:');
        console.error(error);
      }
      
      process.exit(1);
    }
  }

  /**
   * Display help information
   */
  static displayHelp() {
    console.log(`
Usage: node crea_rag.js [options]

Options:
  -v, --verbose     Enable verbose logging
  -f, --force       Force recreation of existing RAG
  -d, --dry-run     Perform a dry run without creating RAG
  -h, --help        Display this help message

Environment Variables:
  STRAICO_API_KEY   Your Straico API key (required)
  RAG_NAME          Name for the RAG (default: Softway-Documents-RAG)
  RAG_DESCRIPTION   Description for the RAG

Examples:
  node crea_rag.js                    # Create RAG with default settings
  node crea_rag.js --verbose          # Create RAG with verbose output
  node crea_rag.js --dry-run          # Preview what would be created
  node crea_rag.js --force --verbose  # Force recreate with verbose output
`);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  RAGCreator.displayHelp();
  process.exit(0);
}

// Create and run RAG creator
const ragCreator = new RAGCreator();
ragCreator.run().catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});