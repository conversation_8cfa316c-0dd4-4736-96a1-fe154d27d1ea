{"name": "chatbot-straico-backend", "version": "1.0.0", "description": "Backend API server for RAG-based chatbot using Straico", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "create-rag": "node scripts/crea_rag.js"}, "dependencies": {"axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "chokidar": "^3.5.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "form-data": "^4.0.3", "fs-extra": "^11.2.0", "helmet": "^7.1.0", "joi": "^17.11.0", "mammoth": "^1.6.0", "marked": "^11.1.1", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["api", "chatbot", "rag", "straico"], "author": "Softway", "license": "MIT"}