# Chatbot Straico - Sistema RAG per Documenti Tecnici

Un'applicazione web completa che utilizza le API di Straico per rispondere a domande basate sui contenuti di file presenti in una directory locale. Il sistema implementa un approccio RAG (Retrieval-Augmented Generation) con backend Node.js e frontend React moderno.

## 🚀 Caratteristiche Principali

- **Chat Interface Moderna**: Interfaccia utente intuitiva e responsive con supporto per markdown
- **Sistema RAG**: Integrazione con Straico per risposte basate sui documenti
- **Gestione File**: Upload e gestione automatica dei documenti
- **Real-time Communication**: Socket.IO per comunicazione in tempo reale
- **Multi-formato**: Supporto per PDF, DOC, TXT, MD, HTML
- **Dashboard Admin**: Pannello di amministrazione per gestione sistema
- **Dark/Light Mode**: Supporto per temi scuro e chiaro
- **Responsive Design**: Ottimizzato per desktop e mobile

## 📁 Struttura del Progetto

```
chatbot-straico/
├── backend/                 # Server Node.js
│   ├── src/
│   │   ├── server.js       # Server principale
│   │   ├── routes/         # Endpoint API
│   │   ├── services/       # Servizi business logic
│   │   ├── middleware/     # Middleware Express
│   │   └── utils/          # Utilità
│   ├── scripts/
│   │   └── crea_rag.js     # Script creazione RAG
│   ├── config/
│   │   └── config.json     # Configurazioni
│   └── package.json
├── frontend/               # Applicazione React
│   ├── src/
│   │   ├── components/     # Componenti React
│   │   ├── services/       # Servizi API
│   │   ├── hooks/          # Custom hooks
│   │   ├── types/          # TypeScript types
│   │   └── App.tsx
│   └── package.json
├── base/                   # Directory file sorgente
│   ├── *.pdf              # File PDF esistenti
│   └── ...
└── README.md
```

## 🛠️ Installazione e Setup

### Prerequisiti

- Node.js 18+ 
- npm o yarn
- Account Straico con API key

### 1. Installazione Dipendenze

```bash
# Installa dipendenze per entrambi backend e frontend
npm run install:all

# Oppure separatamente:
cd backend && npm install
cd frontend && npm install
```

### 2. Configurazione Environment

Crea un file `.env` nella directory `backend/`:

```env
# Straico API Configuration
STRAICO_API_KEY=your_straico_api_key_here
STRAICO_BASE_URL=https://api.straico.com

# Server Configuration
PORT=3001
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000

# File Configuration
BASE_DIRECTORY=../base
UPLOAD_DIRECTORY=./uploads
MAX_FILE_SIZE=10MB

# RAG Configuration
RAG_CHUNK_SIZE=1000
RAG_OVERLAP=200
RAG_MODEL=gpt-3.5-turbo

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log
```

### 3. Creazione RAG

Prima di utilizzare il chatbot, è necessario creare la RAG dai file esistenti:

```bash
cd backend
npm run create-rag

# Con opzioni verbose
node scripts/crea_rag.js --verbose

# Dry run per vedere cosa verrebbe creato
node scripts/crea_rag.js --dry-run
```

### 4. Avvio Applicazione

```bash
# Avvia sia backend che frontend in modalità sviluppo
npm run dev

# Oppure separatamente:
npm run backend:dev    # Backend su porta 3001
npm run frontend:dev   # Frontend su porta 3000
```

## 📖 Utilizzo

### Chat Interface

1. Apri http://localhost:3000
2. Inizia una conversazione facendo domande sui documenti
3. Il sistema utilizzerà la RAG per fornire risposte basate sui contenuti

Esempi di domande:
- "Come si calibra il corpo farfallato?"
- "Quali sono i codici di errore E5?"
- "Procedure di manutenzione Symphony ST200"
- "Specifiche tecniche Euro 5"

### Gestione File

- Vai su `/files` per visualizzare i file nella knowledge base
- Monitora statistiche e metadati dei documenti
- Visualizza informazioni sui tipi di file supportati

### Dashboard Admin

- Vai su `/admin` per accedere al pannello amministrativo
- Crea nuove RAG o aggiorna quelle esistenti
- Monitora statistiche del sistema
- Testa la connessione con Straico

## 🔧 API Endpoints

### Chat
- `POST /api/chat/message` - Invia messaggio
- `GET /api/chat/history/:id` - Cronologia conversazione
- `DELETE /api/chat/conversation/:id` - Elimina conversazione
- `GET /api/chat/conversations` - Lista conversazioni utente
- `POST /api/chat/search` - Cerca nelle conversazioni

### File
- `GET /api/files/list` - Lista file
- `POST /api/files/upload` - Upload file
- `GET /api/files/content/:id` - Contenuto file
- `DELETE /api/files/delete` - Elimina file
- `POST /api/files/process` - Elabora file per RAG

### RAG
- `POST /api/rag/create` - Crea RAG
- `PUT /api/rag/update` - Aggiorna RAG
- `GET /api/rag/status/:id` - Status RAG
- `DELETE /api/rag/:id` - Elimina RAG
- `POST /api/rag/recreate/:id` - Ricrea RAG
- `POST /api/rag/sync/:id` - Sincronizza RAG

## 🔌 Socket.IO Events

### Client → Server
- `user:identify` - Identificazione utente
- `chat:message` - Invio messaggio
- `chat:typing` - Indicatore digitazione
- `conversation:join` - Unisciti conversazione

### Server → Client
- `chat:response` - Risposta chat
- `chat:bot_typing` - Bot sta digitando
- `chat:error` - Errore chat
- `conversation:history` - Cronologia conversazione

## 🎨 Personalizzazione

### Temi
L'applicazione supporta temi chiaro, scuro e automatico (sistema). I temi sono gestiti tramite Tailwind CSS e localStorage.

### Configurazione RAG
Modifica `backend/config/config.json` per personalizzare:
- Dimensione chunk
- Overlap tra chunk
- Modello AI utilizzato
- Parametri di temperatura

### Styling
Il frontend utilizza Tailwind CSS con configurazione personalizzata in `frontend/tailwind.config.js`.

## 🧪 Testing

```bash
# Test backend
cd backend && npm test

# Test frontend
cd frontend && npm test

# Test completo
npm test
```

## 📦 Build e Deploy

### Build Produzione

```bash
# Build frontend
cd frontend && npm run build

# Il build sarà in frontend/build/
```

### Deploy

1. **Backend**: Deploy su server Node.js con PM2
2. **Frontend**: Servi i file statici con nginx
3. **Environment**: Configura variabili di produzione
4. **Database**: Opzionale Redis per caching

### Docker (Opzionale)

```bash
# Build immagini Docker
docker-compose build

# Avvia servizi
docker-compose up -d
```

## 🔍 Troubleshooting

### Problemi Comuni

1. **RAG non creata**: Verifica API key Straico e connessione
2. **File non processati**: Controlla formati supportati e permessi directory
3. **Socket disconnesso**: Verifica configurazione CORS e firewall
4. **Errori upload**: Controlla dimensioni file e spazio disco

### Log

I log sono disponibili in:
- Backend: `backend/logs/app.log`
- Browser: Console sviluppatore
- Socket.IO: Eventi di connessione/disconnessione

### Debug Mode

Avvia in modalità debug:

```bash
NODE_ENV=development DEBUG=* npm run backend:dev
```

## 🤝 Contributi

1. Fork del repository
2. Crea feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit modifiche (`git commit -m 'Add AmazingFeature'`)
4. Push al branch (`git push origin feature/AmazingFeature`)
5. Apri Pull Request

## 📄 Licenza

Questo progetto è sotto licenza MIT. Vedi il file `LICENSE` per dettagli.

## 🆘 Supporto

Per supporto e domande:
- Apri un issue su GitHub
- Consulta la documentazione API
- Verifica i log di sistema

## 🔄 Changelog

### v1.0.0
- Implementazione iniziale
- Chat interface con RAG
- Gestione file automatica
- Dashboard amministrativa
- Socket.IO real-time
- Supporto multi-formato
- Temi scuro/chiaro

---

**Sviluppato per Softway** - Sistema di assistenza tecnica basato su AI